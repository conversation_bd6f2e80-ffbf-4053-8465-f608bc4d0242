package com.crpt.controller;

import com.crpt.client.TianyanchaApiClient;
import com.crpt.entity.ApiResponse;
import com.crpt.entity.TianyanchaResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 测试控制器 - 用于测试天眼查API调用
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {
    
    private static final Logger log = LoggerFactory.getLogger(TestController.class);
    
    @Autowired
    private TianyanchaApiClient tianyanchaApiClient;
    
    /**
     * 测试根据社会信用代码查询
     */
    @GetMapping("/credit-code/{creditCode}")
    public ApiResponse<Object> testByCreditCode(@PathVariable String creditCode) {
        try {
            log.info("=== 开始测试天眼查API ===");
            log.info("社会信用代码: {}", creditCode);

            TianyanchaResponse response = tianyanchaApiClient.searchByCreditCode(creditCode);

            log.info("API响应详情:");
            log.info("- error_code: {}", response.getError_code());
            log.info("- reason: {}", response.getReason());

            if (response.getResult() != null) {
                log.info("- result不为空");
                if (response.getResult().getItems() != null) {
                    log.info("- 查询结果数量: {}", response.getResult().getItems().size());
                    if (!response.getResult().getItems().isEmpty()) {
                        TianyanchaResponse.CompanyInfo firstItem = response.getResult().getItems().get(0);
                        log.info("- 第一条结果: name={}, creditCode={}", firstItem.getName(), firstItem.getCreditCode());
                    }
                } else {
                    log.info("- items为空");
                }
            } else {
                log.info("- result为空");
            }

            // 返回详细的测试结果
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("测试天眼查API失败", e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试根据企业名称查询
     */
    @GetMapping("/company-name/{companyName}")
    public ApiResponse<TianyanchaResponse> testByCompanyName(@PathVariable String companyName) {
        try {
            log.info("测试天眼查API - 企业名称: {}", companyName);
            
            TianyanchaResponse response = tianyanchaApiClient.searchByCompanyName(companyName);
            
            log.info("API响应 - error_code: {}, reason: {}", 
                    response.getError_code(), response.getReason());
            
            if (response.getResult() != null && response.getResult().getItems() != null) {
                log.info("查询结果数量: {}", response.getResult().getItems().size());
            }
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("测试天眼查API失败", e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }
}
