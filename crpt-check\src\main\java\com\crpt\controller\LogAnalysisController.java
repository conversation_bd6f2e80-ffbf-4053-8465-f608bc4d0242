package com.crpt.controller;

import com.crpt.entity.ApiResponse;
import com.crpt.service.LogAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 日志分析控制器
 */
@RestController
@RequestMapping("/api/log")
@CrossOrigin(origins = "*")
public class LogAnalysisController {

    private static final Logger log = LoggerFactory.getLogger(LogAnalysisController.class);
    
    @Autowired
    private LogAnalysisService logAnalysisService;
    
    /**
     * 获取日志文件列表
     */
    @GetMapping("/files")
    public ApiResponse<List<String>> getLogFiles() {
        try {
            List<String> logFiles = logAnalysisService.getLogFiles();
            return ApiResponse.success(logFiles);
        } catch (Exception e) {
            log.error("获取日志文件列表失败", e);
            return ApiResponse.error("获取日志文件列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析日志文件
     */
    @GetMapping("/analyze")
    public ApiResponse<Map<String, Object>> analyzeLog(
            @RequestParam String fileName,
            @RequestParam(required = false) Integer matchTag,
            @RequestParam(required = false) String keyword) {

        try {
            Map<String, Object> result = logAnalysisService.analyzeLogFile(fileName, matchTag, keyword);

            if (result.containsKey("error")) {
                return ApiResponse.error(result.get("error").toString());
            }

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("分析日志文件失败", e);
            return ApiResponse.error("分析日志文件失败: " + e.getMessage());
        }
    }

    /**
     * 保存选择的企业信息
     */
    @PostMapping("/saveSelectedCompany")
    public ApiResponse<String> saveSelectedCompany(@RequestBody Map<String, Object> request) {
        try {
            String logFileName = (String) request.get("logFileName");
            String businessKey = (String) request.get("businessKey");
            String selectedCompanyName = (String) request.get("selectedCompanyName");
            String selectedCreditCode = (String) request.get("selectedCreditCode");
            String selectedCompanyType = (String) request.get("selectedCompanyType");
            String selectedBase = (String) request.get("selectedBase");
            String selectedLegalPersonName = (String) request.get("selectedLegalPersonName");
            String selectedRegStatus = (String) request.get("selectedRegStatus");
            String jsonSource = (String) request.get("jsonSource");
            Integer jsonIndex = (Integer) request.get("jsonIndex");

            // 参数校验
            if (logFileName == null || logFileName.trim().isEmpty()) {
                return ApiResponse.error("日志文件名不能为空");
            }
            if (businessKey == null || businessKey.trim().isEmpty()) {
                return ApiResponse.error("业务主键不能为空");
            }

            log.info("保存选择的企业信息 - 日志文件: {}, 业务主键: {}, 企业名称: {}, 社会信用代码: {}, JSON来源: {}, 索引: {}",
                    logFileName, businessKey, selectedCompanyName, selectedCreditCode, jsonSource, jsonIndex);

            // 调用服务保存选择的企业信息
            logAnalysisService.saveSelectedCompanyInfo(logFileName, businessKey, selectedCompanyName,
                    selectedCreditCode, selectedCompanyType, selectedBase,
                    selectedLegalPersonName, selectedRegStatus, jsonSource, jsonIndex);

            return ApiResponse.success("选择的企业信息保存成功");

        } catch (Exception e) {
            log.error("保存选择的企业信息失败", e);
            return ApiResponse.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除选择的企业信息
     */
    @PostMapping("/deleteSelectedCompany")
    public ApiResponse<String> deleteSelectedCompany(@RequestBody Map<String, Object> request) {
        try {
            String logFileName = (String) request.get("logFileName");
            String businessKey = (String) request.get("businessKey");

            // 参数校验
            if (logFileName == null || logFileName.trim().isEmpty()) {
                return ApiResponse.error("日志文件名不能为空");
            }
            if (businessKey == null || businessKey.trim().isEmpty()) {
                return ApiResponse.error("业务主键不能为空");
            }

            log.info("删除选择的企业信息 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);

            // 调用服务删除选择的企业信息
            logAnalysisService.deleteSelectedCompanyInfo(logFileName, businessKey);

            return ApiResponse.success("选择的企业信息删除成功");

        } catch (Exception e) {
            log.error("删除选择的企业信息失败", e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定日志文件的所有已选择企业信息
     */
    @GetMapping("/getSelectedCompanies")
    public ApiResponse<Map<String, Map<String, Object>>> getSelectedCompanies(@RequestParam String logFileName) {
        try {
            // 参数校验
            if (logFileName == null || logFileName.trim().isEmpty()) {
                return ApiResponse.error("日志文件名不能为空");
            }

            log.info("获取已选择企业信息 - 日志文件: {}", logFileName);

            // 调用服务获取已选择的企业信息
            Map<String, Map<String, Object>> selectedCompanies = logAnalysisService.getAllSelectedCompanies(logFileName);

            return ApiResponse.success(selectedCompanies);

        } catch (Exception e) {
            log.error("获取已选择企业信息失败", e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 导出分析结果为CSV
     */
    @GetMapping("/export")
    public ResponseEntity<Resource> exportToCsv(
            @RequestParam String fileName,
            @RequestParam(required = false) Integer matchTag,
            @RequestParam(required = false) String keyword) {
        
        try {
            String csvFileName = logAnalysisService.exportToCsv(fileName, matchTag, keyword);
            
            if (csvFileName == null) {
                return ResponseEntity.notFound().build();
            }
            
            File csvFile = new File(logAnalysisService.getLogFilePath(), csvFileName);
            Resource resource = new FileSystemResource(csvFile);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + csvFileName + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出CSV失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 更新日志文件中的配置信息
     */
    @PostMapping("/updateConfig")
    public ApiResponse<String> updateConfigInfo(
            @RequestParam String fileName,
            @RequestBody Map<String, Object> updatedConfig) {

        try {
            log.info("更新配置信息 - 日志文件: {}", fileName);

            boolean success = logAnalysisService.updateConfigInfo(fileName, updatedConfig);

            if (success) {
                return ApiResponse.success("配置信息更新成功");
            } else {
                return ApiResponse.error("配置信息更新失败");
            }

        } catch (Exception e) {
            log.error("更新配置信息失败", e);
            return ApiResponse.error("更新配置信息失败: " + e.getMessage());
        }
    }
}
