package com.crpt.service;

import com.crpt.entity.MatchResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 日志分析服务
 */
@Service
public class LogAnalysisService {

    private static final Logger log = LoggerFactory.getLogger(LogAnalysisService.class);
    
    @Value("${crpt.check.log-file-path}")
    private String logFilePath;

    @Autowired
    private ProcessedStatusService processedStatusService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 选择信息存储目录
    private static final String SELECTED_COMPANIES_DIR = "logs/selected-companies";

    // 内存缓存，格式：logFileName -> (businessKey -> selectedCompanyInfo)
    private final Map<String, Map<String, Map<String, Object>>> selectedCompaniesCache = new ConcurrentHashMap<>();
    
    /**
     * 获取所有日志文件列表
     */
    public List<String> getLogFiles() {
        try {
            File logDir = new File(logFilePath);
            if (!logDir.exists() || !logDir.isDirectory()) {
                return new ArrayList<>();
            }
            
            return Arrays.stream(logDir.listFiles())
                    .filter(file -> file.isFile() && file.getName().endsWith(".log"))
                    .map(File::getName)
                    .sorted(Collections.reverseOrder()) // 按时间倒序
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取日志文件列表失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 分析指定日志文件
     */
    public Map<String, Object> analyzeLogFile(String fileName, Integer matchTag, String keyword) {
        Map<String, Object> result = new HashMap<>();
        List<MatchResult> matchResults = new ArrayList<>();
        Map<Integer, Integer> tagStatistics = new HashMap<>();
        Map<String, Object> configInfo = null;

        try {
            File logFile = new File(logFilePath, fileName);
            if (!logFile.exists()) {
                result.put("error", "日志文件不存在: " + fileName);
                return result;
            }

            // 读取并解析日志文件
            try (BufferedReader reader = new BufferedReader(new FileReader(logFile))) {
                String line;

                while ((line = reader.readLine()) != null) {
                    String trimmedLine = line.trim();

                    // 跳过空行和注释行（以#开头的行）
                    if (trimmedLine.isEmpty() || trimmedLine.startsWith("#")) {
                        continue;
                    }

                    // 只解析看起来像JSON的行（以{开头，以}结尾）
                    if (!trimmedLine.startsWith("{") || !trimmedLine.endsWith("}")) {
                        continue;
                    }

                    try {
                        // 首先尝试判断是否为配置信息
                        if (isConfigurationInfo(trimmedLine)) {
                            // 提取配置信息
                            if (configInfo == null) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> config = objectMapper.readValue(trimmedLine, Map.class);
                                configInfo = config;
                                log.debug("提取配置信息: {}", configInfo.get("timestamp"));
                            }
                            continue;
                        }

                        // 尝试解析为匹配结果
                        MatchResult matchResult = objectMapper.readValue(line, MatchResult.class);

                        // 设置处理状态
                        boolean isProcessed = processedStatusService.isProcessed(fileName, matchResult.getBusinessKey());
                        matchResult.setProcessed(isProcessed);

                        // 应用过滤条件
                        if (matchesFilter(matchResult, matchTag, keyword)) {
                            matchResults.add(matchResult);
                        }

                        // 统计各种匹配标识的数量
                        Integer tag = matchResult.getMatchTag();
                        tagStatistics.put(tag, tagStatistics.getOrDefault(tag, 0) + 1);

                    } catch (Exception e) {
                        log.warn("解析日志行失败: {}", line, e);
                    }
                }
            }

            // 构建返回结果
            result.put("fileName", fileName);
            result.put("totalCount", matchResults.size());
            result.put("matchResults", matchResults);
            result.put("statistics", tagStatistics);
            result.put("tagDescriptions", getTagDescriptions());
            result.put("configInfo", configInfo); // 添加配置信息

        } catch (Exception e) {
            log.error("分析日志文件失败: {}", fileName, e);
            result.put("error", "分析日志文件失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 检查匹配结果是否符合过滤条件
     */
    private boolean matchesFilter(MatchResult matchResult, Integer matchTag, String keyword) {
        // 匹配标识过滤
        if (matchTag != null && !matchTag.equals(matchResult.getMatchTag())) {
            return false;
        }
        
        // 关键词过滤
        if (keyword != null && !keyword.trim().isEmpty()) {
            String lowerKeyword = keyword.toLowerCase();
            return (matchResult.getOriginalCompanyName() != null && 
                    matchResult.getOriginalCompanyName().toLowerCase().contains(lowerKeyword)) ||
                   (matchResult.getOriginalCreditCode() != null && 
                    matchResult.getOriginalCreditCode().toLowerCase().contains(lowerKeyword)) ||
                   (matchResult.getBusinessKey() != null && 
                    matchResult.getBusinessKey().toLowerCase().contains(lowerKeyword));
        }
        
        return true;
    }

    /**
     * 判断JSON行是否为配置信息
     */
    private boolean isConfigurationInfo(String jsonLine) {
        try {
            // 尝试解析为通用的Map来检查是否包含配置信息的特征字段
            @SuppressWarnings("unchecked")
            Map<String, Object> jsonMap = objectMapper.readValue(jsonLine, Map.class);

            // 检查是否包含配置信息的特征字段
            return jsonMap.containsKey("configType") &&
                   "MATCH_CHECK_CONFIG".equals(jsonMap.get("configType"));

        } catch (Exception e) {
            // 如果解析失败，返回false，让后续逻辑处理
            return false;
        }
    }

    /**
     * 获取匹配标识描述
     */
    private Map<Integer, String> getTagDescriptions() {
        Map<Integer, String> descriptions = new HashMap<>();
        descriptions.put(1, "单位名称和社会信用代码全部匹配成功");
        descriptions.put(2, "社会信用代码匹配成功，单位名称未匹配成功");
        descriptions.put(3, "单位名称匹配成功，社会信用代码未匹配成功");
        descriptions.put(4, "单位名称和社会信用代码全部匹配失败");
        descriptions.put(5, "单位名称和社会信用代码都能匹配到天眼查，但不是同一个单位");
        return descriptions;
    }
    
    /**
     * 导出分析结果为CSV
     */
    public String exportToCsv(String fileName, Integer matchTag, String keyword) {
        try {
            Map<String, Object> analysisResult = analyzeLogFile(fileName, matchTag, keyword);
            @SuppressWarnings("unchecked")
            List<MatchResult> matchResults = (List<MatchResult>) analysisResult.get("matchResults");
            
            if (matchResults == null || matchResults.isEmpty()) {
                return null;
            }
            
            // 生成CSV文件
            String csvFileName = "export-" + System.currentTimeMillis() + ".csv";
            File csvFile = new File(logFilePath, csvFileName);
            
            try (PrintWriter writer = new PrintWriter(new FileWriter(csvFile))) {
                // 写入CSV头部
                writer.println("业务主键,原始单位名称,原始社会信用代码,匹配标识,匹配标识描述,处理时间");
                
                Map<Integer, String> tagDescriptions = getTagDescriptions();
                
                // 写入数据行
                for (MatchResult result : matchResults) {
                    writer.printf("%s,%s,%s,%d,%s,%s%n",
                            escapeCSV(result.getBusinessKey()),
                            escapeCSV(result.getOriginalCompanyName()),
                            escapeCSV(result.getOriginalCreditCode()),
                            result.getMatchTag(),
                            escapeCSV(tagDescriptions.get(result.getMatchTag())),
                            escapeCSV(result.getProcessTime()));
                }
            }
            
            return csvFileName;
            
        } catch (Exception e) {
            log.error("导出CSV失败", e);
            return null;
        }
    }
    
    /**
     * CSV字段转义
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    /**
     * 获取日志文件路径
     */
    public String getLogFilePath() {
        return logFilePath;
    }

    /**
     * 初始化选择信息存储目录
     */
    private void initSelectedCompaniesDirectory() {
        File selectedDir = new File(SELECTED_COMPANIES_DIR);
        if (!selectedDir.exists()) {
            boolean created = selectedDir.mkdirs();
            if (created) {
                log.info("创建选择信息存储目录: {}", SELECTED_COMPANIES_DIR);
            } else {
                log.warn("创建选择信息存储目录失败: {}", SELECTED_COMPANIES_DIR);
            }
        }
    }

    /**
     * 获取选择信息文件路径
     */
    private String getSelectedCompaniesFilePath(String logFileName) {
        return SELECTED_COMPANIES_DIR + "/" + logFileName + ".selected.json";
    }

    /**
     * 加载指定日志文件的选择信息
     */
    private Map<String, Map<String, Object>> loadSelectedCompanies(String logFileName) {
        if (!StringUtils.hasText(logFileName)) {
            return new HashMap<>();
        }

        // 先检查缓存
        if (selectedCompaniesCache.containsKey(logFileName)) {
            return selectedCompaniesCache.get(logFileName);
        }

        // 从文件加载
        String selectedFilePath = getSelectedCompaniesFilePath(logFileName);
        File selectedFile = new File(selectedFilePath);

        Map<String, Map<String, Object>> selectedMap = new HashMap<>();

        if (selectedFile.exists()) {
            try {
                TypeReference<Map<String, Map<String, Object>>> typeRef = new TypeReference<Map<String, Map<String, Object>>>() {};
                selectedMap = objectMapper.readValue(selectedFile, typeRef);
                log.debug("从文件加载选择信息: {} -> {} 条记录", logFileName, selectedMap.size());
            } catch (IOException e) {
                log.warn("加载选择信息文件失败: {}", selectedFilePath, e);
            }
        }

        // 缓存到内存
        selectedCompaniesCache.put(logFileName, selectedMap);
        return selectedMap;
    }

    /**
     * 保存指定日志文件的选择信息
     */
    private void saveSelectedCompanies(String logFileName, Map<String, Map<String, Object>> selectedMap) {
        if (!StringUtils.hasText(logFileName) || selectedMap == null) {
            return;
        }

        initSelectedCompaniesDirectory();

        String selectedFilePath = getSelectedCompaniesFilePath(logFileName);

        try {
            objectMapper.writeValue(new File(selectedFilePath), selectedMap);
            log.debug("保存选择信息到文件: {} -> {} 条记录", logFileName, selectedMap.size());
        } catch (IOException e) {
            log.error("保存选择信息文件失败: {}", selectedFilePath, e);
        }
    }

    /**
     * 保存选择的企业信息
     */
    public void saveSelectedCompanyInfo(String logFileName, String businessKey, String selectedCompanyName,
            String selectedCreditCode, String selectedCompanyType, String selectedBase,
            String selectedLegalPersonName, String selectedRegStatus, String jsonSource, Integer jsonIndex) {
        try {
            log.info("保存选择的企业信息 - 日志文件: {}, 业务主键: {}, 企业名称: {}, 社会信用代码: {}, JSON来源: {}, 索引: {}",
                    logFileName, businessKey, selectedCompanyName, selectedCreditCode, jsonSource, jsonIndex);

            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                throw new IllegalArgumentException("日志文件名不能为空");
            }
            if (!StringUtils.hasText(businessKey)) {
                throw new IllegalArgumentException("业务主键不能为空");
            }

            // 加载当前选择信息
            Map<String, Map<String, Object>> selectedMap = loadSelectedCompanies(logFileName);

            // 查找对应的匹配结果以获取更新SQL
            String updateSql = findUpdateSqlForBusinessKey(logFileName, businessKey);

            // 创建选择记录
            Map<String, Object> selectionRecord = new HashMap<>();
            selectionRecord.put("businessKey", businessKey);
            selectionRecord.put("selectedCompanyName", selectedCompanyName);
            selectionRecord.put("selectedCreditCode", selectedCreditCode);
            selectionRecord.put("selectedCompanyType", selectedCompanyType);
            selectionRecord.put("selectedBase", selectedBase);
            selectionRecord.put("selectedLegalPersonName", selectedLegalPersonName);
            selectionRecord.put("selectedRegStatus", selectedRegStatus);
            selectionRecord.put("jsonSource", jsonSource); // 记录来源：creditCode 或 companyName
            selectionRecord.put("jsonIndex", jsonIndex);   // 记录在原始JSON数组中的索引
            selectionRecord.put("selectionTime", new Date().toString());

            // 添加更新SQL信息
            if (StringUtils.hasText(updateSql)) {
                // 生成实际的更新SQL（替换占位符）
                String actualUpdateSql = generateActualUpdateSql(updateSql, selectedCompanyName, selectedCreditCode, businessKey);
                selectionRecord.put("updateSql", actualUpdateSql);
                log.info("记录更新SQL到选择信息 - 业务主键: {}, SQL: {}", businessKey, actualUpdateSql);
            }

            // 保存选择记录
            selectedMap.put(businessKey, selectionRecord);

            // 更新缓存
            selectedCompaniesCache.put(logFileName, selectedMap);

            // 保存到文件
            saveSelectedCompanies(logFileName, selectedMap);

            log.info("选择的企业信息保存成功 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);

        } catch (Exception e) {
            log.error("保存选择的企业信息失败 - 日志文件: {}, 业务主键: {}", logFileName, businessKey, e);
            throw new RuntimeException("保存选择的企业信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除选择的企业信息
     */
    public void deleteSelectedCompanyInfo(String logFileName, String businessKey) {
        try {
            log.info("删除选择的企业信息 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);

            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                throw new IllegalArgumentException("日志文件名不能为空");
            }
            if (!StringUtils.hasText(businessKey)) {
                throw new IllegalArgumentException("业务主键不能为空");
            }

            // 加载当前选择信息
            Map<String, Map<String, Object>> selectedMap = loadSelectedCompanies(logFileName);

            // 删除选择记录
            selectedMap.remove(businessKey);

            // 更新缓存
            selectedCompaniesCache.put(logFileName, selectedMap);

            // 保存到文件
            saveSelectedCompanies(logFileName, selectedMap);

            log.info("选择的企业信息删除成功 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);

        } catch (Exception e) {
            log.error("删除选择的企业信息失败 - 日志文件: {}, 业务主键: {}", logFileName, businessKey, e);
            throw new RuntimeException("删除选择的企业信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取指定日志文件的所有已选择企业信息
     */
    public Map<String, Map<String, Object>> getAllSelectedCompanies(String logFileName) {
        if (!StringUtils.hasText(logFileName)) {
            return new HashMap<>();
        }

        return new HashMap<>(loadSelectedCompanies(logFileName));
    }

    /**
     * 查找指定日志文件对应的更新SQL模板（从配置信息中获取）
     */
    private String findUpdateSqlForBusinessKey(String logFileName, String businessKey) {
        try {
            File logFile = new File(logFilePath, logFileName);
            if (!logFile.exists()) {
                log.warn("日志文件不存在: {}", logFile.getAbsolutePath());
                return null;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(logFile))) {
                String line;

                while ((line = reader.readLine()) != null) {
                    String trimmedLine = line.trim();

                    if (trimmedLine.isEmpty()) continue;

                    // 只解析看起来像JSON的行
                    if (!trimmedLine.startsWith("{") || !trimmedLine.endsWith("}")) {
                        continue;
                    }

                    try {
                        // 查找配置信息
                        if (isConfigurationInfo(trimmedLine)) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> config = objectMapper.readValue(trimmedLine, Map.class);
                            String updateSql = (String) config.get("updateSql");
                            if (StringUtils.hasText(updateSql)) {
                                log.debug("从配置信息中获取更新SQL模板: {}", updateSql);
                                return updateSql;
                            }
                        }
                    } catch (Exception e) {
                        log.debug("解析日志行失败，跳过: {}", line);
                    }
                }
            }
        } catch (Exception e) {
            log.error("查找更新SQL失败 - 日志文件: {}, 业务主键: {}", logFileName, businessKey, e);
        }

        return null;
    }

    /**
     * 生成实际的更新SQL（替换占位符）
     * 使用选择的企业信息替换占位符 - 支持新的语义化占位符
     */
    private String generateActualUpdateSql(String updateSqlTemplate, String selectedCompanyName, String selectedCreditCode, String businessKey) {
        if (!StringUtils.hasText(updateSqlTemplate)) {
            return updateSqlTemplate;
        }

        log.debug("原始更新SQL模板: {}", updateSqlTemplate);
        String actualSql = updateSqlTemplate;

        // 替换新的语义化占位符 - 企业名称（使用选择的企业名称）
        if (StringUtils.hasText(selectedCompanyName)) {
            actualSql = actualSql.replace("{newCompanyName}", selectedCompanyName);
            actualSql = actualSql.replace("${newCompanyName}", selectedCompanyName);
            actualSql = actualSql.replace("${NEW_COMPANY_NAME}", selectedCompanyName);

            // 保持向后兼容性
            actualSql = actualSql.replace("{companyName}", selectedCompanyName);
            actualSql = actualSql.replace("${companyName}", selectedCompanyName);
            actualSql = actualSql.replace("${COMPANY_NAME}", selectedCompanyName);
        }

        // 替换新的语义化占位符 - 社会信用代码（使用选择的社会信用代码）
        if (StringUtils.hasText(selectedCreditCode)) {
            actualSql = actualSql.replace("{newCreditCode}", selectedCreditCode);
            actualSql = actualSql.replace("${newCreditCode}", selectedCreditCode);
            actualSql = actualSql.replace("${NEW_CREDIT_CODE}", selectedCreditCode);

            // 保持向后兼容性
            actualSql = actualSql.replace("{creditCode}", selectedCreditCode);
            actualSql = actualSql.replace("${creditCode}", selectedCreditCode);
            actualSql = actualSql.replace("${CREDIT_CODE}", selectedCreditCode);
        }

        // 替换业务主键占位符
        if (StringUtils.hasText(businessKey)) {
            actualSql = actualSql.replace("{businessKey}", businessKey);
            actualSql = actualSql.replace("${businessKey}", businessKey);
            actualSql = actualSql.replace("${BUSINESS_KEY}", businessKey);
        }

        log.debug("生成的实际更新SQL: {}", actualSql);
        return actualSql;
    }

    /**
     * 更新日志文件中的配置信息
     */
    public boolean updateConfigInfo(String fileName, Map<String, Object> updatedConfig) {
        try {
            File logFile = new File(logFilePath, fileName);
            if (!logFile.exists()) {
                log.error("日志文件不存在: {}", fileName);
                return false;
            }

            List<String> lines = new ArrayList<>();
            boolean configUpdated = false;

            // 读取所有行
            try (BufferedReader reader = new BufferedReader(new FileReader(logFile))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String trimmedLine = line.trim();

                    // 检查是否为配置信息行
                    if (trimmedLine.startsWith("{") && trimmedLine.endsWith("}") && isConfigurationInfo(trimmedLine)) {
                        if (!configUpdated) {
                            // 更新配置信息
                            @SuppressWarnings("unchecked")
                            Map<String, Object> existingConfig = objectMapper.readValue(trimmedLine, Map.class);

                            // 合并更新的配置
                            existingConfig.putAll(updatedConfig);

                            // 更新时间戳
                            existingConfig.put("timestamp", java.time.LocalDateTime.now()
                                    .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                            // 转换为JSON字符串
                            String updatedConfigJson = objectMapper.writeValueAsString(existingConfig);
                            lines.add(updatedConfigJson);
                            configUpdated = true;

                            log.info("更新配置信息: {}", fileName);
                        } else {
                            // 跳过重复的配置信息行
                            continue;
                        }
                    } else {
                        // 保留其他行
                        lines.add(line);
                    }
                }
            }

            // 写回文件
            try (PrintWriter writer = new PrintWriter(new FileWriter(logFile))) {
                for (String line : lines) {
                    writer.println(line);
                }
            }

            return configUpdated;

        } catch (Exception e) {
            log.error("更新配置信息失败: {}", fileName, e);
            return false;
        }
    }
}
