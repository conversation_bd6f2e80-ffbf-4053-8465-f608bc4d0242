# 天眼查企业信息匹配系统 - 用户操作手册

## 匹配天眼查前要求

### 1. 专网需开通支持调用天眼查外网接口

### 2. 备份数据库

### 3. 历史数据处理

**示例:**

```sql
# 社会信用代码全部转大写
# 职卫 Oracle
UPDATE TB_TJ_CRPT
SET INSTITUTION_CODE = UPPER(INSTITUTION_CODE);
# 监管 Mysql
UPDATE TD_EMPLOYER_BASIC
SET CREDIT_CODE = UPPER(CREDIT_CODE);

# 单位名称的前后空格处理
# 职卫 Oracle
UPDATE TB_TJ_CRPT 
SET CRPT_NAME = REGEXP_REPLACE(CRPT_NAME, '^[　 ]+|[　 ]+$', '')
WHERE CRPT_NAME <> REGEXP_REPLACE(CRPT_NAME, '^[　 ]+|[　 ]+$', '');
# 监管 Mysql
UPDATE TD_EMPLOYER_BASIC
SET UNIT_NAME = TRIM(BOTH '　' FROM TRIM(UNIT_NAME))
WHERE UNIT_NAME <> TRIM(BOTH '　' FROM TRIM(UNIT_NAME));
```

### 4. 单位名称或社会信用代码为空的数据,处理成不为空(手动处理)

### 5. 单位名称+社会信用代码重复,手动处理(单位名称+社会信用代码保证不能为空且唯一)

```sql
# 职卫
SELECT INSTITUTION_CODE, CRPT_NAME
FROM TB_TJ_CRPT
GROUP BY INSTITUTION_CODE, CRPT_NAME
HAVING COUNT(1) > 1;
# 监管
SELECT CREDIT_CODE, UNIT_NAME
FROM TD_EMPLOYER_BASIC
GROUP BY CREDIT_CODE, UNIT_NAME
HAVING COUNT(1) > 1;
```

### 6. 主体机构社会信用代码重复,手动处理(主体机构社会信用代码保证不能为空且唯一)

```sql
# 职卫
SELECT INSTITUTION_CODE
FROM TB_TJ_CRPT
WHERE IF_SUB_ORG IS NULL
   OR IF_SUB_ORG = 0
GROUP BY INSTITUTION_CODE
HAVING COUNT(1) > 1;
# 监管
SELECT CREDIT_CODE
FROM TD_EMPLOYER_BASIC
WHERE IF_BRANCH IS NULL
   OR IF_BRANCH = 0
GROUP BY CREDIT_CODE
HAVING COUNT(1) > 1;
```

## 1. 系统概述

天眼查企业信息匹配系统是一个基于Spring Boot开发的企业信息核验工具，通过调用天眼查API对数据库中的企业信息进行匹配验证，支持MySQL和Oracle数据库。

### 1.1 主要功能

- 企业信息匹配检查
- 日志分析与统计
- 企业信息选择与更新
- AI智能分析（可选）
- 处理状态管理

## 2. 系统部署与配置

### 2.1 环境要求

- JDK 1.8+

### 2.2 配置文件说明

#### 2.2.1 数据库配置 (application.yml)

```yaml
spring:
  datasource:
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *******************************
      username: your_username
      password: your_password
    oracle:
      driver-class-name: oracle.jdbc.OracleDriver
      url: *******************************
      username: your_username
      password: your_password
```

**调整分页大小**

   ```yaml
   crpt:
     check:
       page-size: 1000
   ```

#### 2.2.2 天眼查API配置

```yaml
tianyancha:
  api:
    base-url: https://open.api.tianyancha.com
    token: your_tianyancha_token
    search-endpoint: /services/open/search/2.0
```

**调整API调用间隔**

   ```yaml
   crpt:
     check:
       api-interval: 100 # 毫秒
   ```

#### 2.2.3 AI分析配置（可选）

```yaml
ai:
  analysis:
    enabled: true  # 设置为true启用AI分析
    api-url: https://api.deepseek.com/chat/completions
    api-key: your_api_key
    model: deepseek-chat
    timeout: 30
```

### 2.3 启动系统

```bash
# 启动应用
java -jar crpt-check-1.0.0.jar
```

系统启动后访问: http://localhost:8094

## 3. 功能模块详解

### 3.1 企业信息匹配检查

#### 3.1.1 功能说明

对数据库中的企业信息与天眼查数据进行匹配验证，生成匹配结果日志。

#### 3.1.2 操作步骤

1. **选择数据库类型**
    - MySQL
    - Oracle

2. **输入SQL查询语句**
    - 必须使用别名来区分字段（ID: 业务主键，CODE: 社会信用代码，NAME: 名称，IF_SUB_ORG: 是否分支机构，DEL_MARK: 删除标记）
    - 可以查看示例

3. **填写表名**
    - 系统会自动从SQL中解析表名，需要检查
    - 也可手动输入

4. **配置字段映射**
    - 社会信用代码列名
    - 单位名称列名
    - 业务主键列名
    - 删除标记列名
    - 分支机构标记列名

5. **配置更新SQL模板**
    - 用于后续更新企业信息
    - 支持占位符：表名称{tableName}、新社会信用代码{newCreditCode}、原社会信用代码{oldCreditCode}、新单位名称{newCompanyName}、原单位名称{oldCompanyName}、主键值{businessKey}
    - 根据主键更新名称和代码,根据旧代码更新分支机构的代码

6. **启动检查**
    - 点击"开始检查"按钮
    - 系统异步执行，自动跳转到日志分析页面

#### 3.1.3 匹配规则

系统按以下逻辑进行匹配：

1. **根据社会信用代码查询天眼查**
    - 有数据且完全匹配 → match_tag=1（成功）
    - 有数据但不完全匹配 → 继续按企业名称查询

2. **根据企业名称查询天眼查**
    - 无数据 → match_tag=4（都查不到）
    - 有数据且完全匹配 → match_tag=1（成功）
    - 企业名称匹配但社会信用代码不匹配 → match_tag=3
    - 都不匹配 → match_tag=4

3. **匹配标识说明**
    - `1`: 单位名称和社会信用代码全部匹配成功
    - `2`: 社会信用代码匹配成功，单位名称未匹配成功
    - `3`: 单位名称匹配成功，社会信用代码未匹配成功
    - `4`: 单位名称和社会信用代码全部匹配失败
    - `5`: 都能匹配到天眼查，但不是同一个单位

### 3.2 日志分析

#### 3.2.1 功能说明

分析匹配检查生成的日志文件，提供统计信息和详细记录查看。

#### 3.2.2 操作步骤

1. **选择日志文件**
    - 从下拉列表中选择要分析的日志文件
    - 文件名格式：数据库类型_表名_开始时间

2. **查看统计信息**
    - 各匹配类型的数量统计
    - 点击统计数字可按类型筛选

3. **筛选和搜索**
    - 按匹配标识筛选
    - 关键词搜索（支持企业名称、社会信用代码等）

4. **查看详细信息**
    - 点击记录查看API返回的详细JSON数据
    - 查看AI分析结果（如果启用）

#### 3.2.3 页面功能

- **自动刷新**: 可开启自动刷新功能
- **分页显示**: 支持分页浏览大量数据
- **复制功能**: 点击企业名称、社会信用代码等可复制到剪贴板
- **处理标记**: 对异常记录可标记为"已处理"

### 3.3 企业信息选择与更新

#### 3.3.1 功能说明

对匹配失败的记录，可从天眼查返回的候选企业中选择正确的企业信息。

#### 3.3.2 操作步骤

1. **选择企业**
    - 在日志分析页面点击"处理"按钮
    - 在弹出的模态框中查看：
        - 左侧：原始查询信息 + AI分析 + 查询详情
        - 右侧：候选企业列表

2. **企业信息对比**
    - 查看原始信息与候选企业的差异
    - AI推荐的企业会有特殊标记
    - 已选择的企业显示在列表顶部

3. **确认选择**
    - 选择合适的企业信息
    - 系统显示确认对话框，包含：
        - 原始信息
        - 新选择的信息
        - 高亮显示变更内容

4. **执行更新**
    - 确认后系统生成并执行更新SQL
    - 更新数据库中的企业信息

#### 3.3.3 撤销操作

- 可撤销已选择的企业信息
- 系统生成回滚SQL恢复原始数据
- 支持查看将要执行的SQL语句

### 3.4 AI智能分析

#### 3.4.1 功能说明

对匹配失败的记录进行AI分析，提供失败原因和建议。

#### 3.4.2 配置要求

1. **启用AI分析**
   ```yaml
   ai:
     analysis:
       enabled: true
   ```

2. **配置API参数**
    - API地址：支持OpenAI兼容格式
    - API密钥：从服务提供商获取
    - 模型名称：如deepseek-chat、gpt-3.5-turbo等

#### 3.4.3 分析结果

AI分析结果以Markdown格式显示，包含：

- 原始信息分析
- 可能的匹配信息（突出差异）
- 匹配失败的原因分析

## 4. 治理后的档案重复数据合并

**前提：所有用人单位的档案数据全部治理完成后，且无任何问题后才可执行以下操作，否则无法回退。**

### 4.1 单位名称+社会信用代码重复

```sql
# 职卫
SELECT INSTITUTION_CODE, CRPT_NAME
FROM TB_TJ_CRPT
GROUP BY INSTITUTION_CODE, CRPT_NAME
HAVING COUNT(1) > 1;
# 监管
SELECT CREDIT_CODE, UNIT_NAME
FROM TD_EMPLOYER_BASIC
GROUP BY CREDIT_CODE, UNIT_NAME
HAVING COUNT(1) > 1;
```

#### 4.1.1 职卫平台

通过原有功能用人单位合并实现。

#### 4.1.3 监管平台

被合并的用人单位相关业务数据，修改为保留的用人单位进行关联。(待提供SQL)

### 4.2 主体机构社会信用代码重复

```sql
# 职卫
SELECT INSTITUTION_CODE
FROM TB_TJ_CRPT
WHERE IF_SUB_ORG IS NULL
   OR IF_SUB_ORG = 0
GROUP BY INSTITUTION_CODE
HAVING COUNT(1) > 1;
# 监管
SELECT CREDIT_CODE
FROM TD_EMPLOYER_BASIC
WHERE IF_BRANCH IS NULL
   OR IF_BRANCH = 0
GROUP BY CREDIT_CODE
HAVING COUNT(1) > 1;
```

#### 4.2.1 职卫平台

通过原有功能用人单位合并实现。

#### 4.2.2 监管平台

将被合并的用人单位相关业务数据，修改为保留的用人单位进行关联。(待提供SQL)
