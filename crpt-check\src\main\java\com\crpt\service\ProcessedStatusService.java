package com.crpt.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 处理状态管理服务
 * 用于管理日志记录的已处理状态
 */
@Service
public class ProcessedStatusService {

    private static final Logger log = LoggerFactory.getLogger(ProcessedStatusService.class);

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 内存缓存，格式：logFileName -> (businessKey -> processed)
    private final Map<String, Map<String, Boolean>> processedStatusCache = new ConcurrentHashMap<>();
    
    // 状态文件存储目录
    private static final String STATUS_DIR = "logs/processed-status";
    
    /**
     * 初始化状态存储目录
     */
    private void initStatusDirectory() {
        File statusDir = new File(STATUS_DIR);
        if (!statusDir.exists()) {
            boolean created = statusDir.mkdirs();
            if (created) {
                log.info("创建处理状态存储目录: {}", STATUS_DIR);
            } else {
                log.warn("创建处理状态存储目录失败: {}", STATUS_DIR);
            }
        }
    }
    
    /**
     * 获取状态文件路径
     */
    private String getStatusFilePath(String logFileName) {
        return STATUS_DIR + "/" + logFileName + ".status.json";
    }
    
    /**
     * 加载指定日志文件的处理状态
     */
    private Map<String, Boolean> loadProcessedStatus(String logFileName) {
        if (!StringUtils.hasText(logFileName)) {
            return new HashMap<>();
        }
        
        // 先检查缓存
        if (processedStatusCache.containsKey(logFileName)) {
            return processedStatusCache.get(logFileName);
        }
        
        // 从文件加载
        String statusFilePath = getStatusFilePath(logFileName);
        File statusFile = new File(statusFilePath);
        
        Map<String, Boolean> statusMap = new HashMap<>();
        
        if (statusFile.exists()) {
            try {
                TypeReference<Map<String, Boolean>> typeRef = new TypeReference<Map<String, Boolean>>() {};
                statusMap = objectMapper.readValue(statusFile, typeRef);
                log.debug("从文件加载处理状态: {} -> {} 条记录", logFileName, statusMap.size());
            } catch (IOException e) {
                log.warn("加载处理状态文件失败: {}", statusFilePath, e);
            }
        }
        
        // 缓存到内存
        processedStatusCache.put(logFileName, statusMap);
        return statusMap;
    }
    
    /**
     * 保存指定日志文件的处理状态
     */
    private void saveProcessedStatus(String logFileName, Map<String, Boolean> statusMap) {
        if (!StringUtils.hasText(logFileName) || statusMap == null) {
            return;
        }
        
        initStatusDirectory();
        
        String statusFilePath = getStatusFilePath(logFileName);
        
        try {
            objectMapper.writeValue(new File(statusFilePath), statusMap);
            log.debug("保存处理状态到文件: {} -> {} 条记录", logFileName, statusMap.size());
        } catch (IOException e) {
            log.error("保存处理状态文件失败: {}", statusFilePath, e);
        }
    }
    
    /**
     * 标记记录为已处理
     */
    public void markAsProcessed(String logFileName, String businessKey) {
        if (!StringUtils.hasText(logFileName) || !StringUtils.hasText(businessKey)) {
            log.warn("标记已处理失败：参数不能为空 - logFileName: {}, businessKey: {}", logFileName, businessKey);
            return;
        }
        
        Map<String, Boolean> statusMap = loadProcessedStatus(logFileName);
        statusMap.put(businessKey, true);
        
        // 更新缓存
        processedStatusCache.put(logFileName, statusMap);
        
        // 保存到文件
        saveProcessedStatus(logFileName, statusMap);
        
        log.info("标记记录为已处理 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);
    }
    
    /**
     * 取消已处理标记
     */
    public void unmarkAsProcessed(String logFileName, String businessKey) {
        if (!StringUtils.hasText(logFileName) || !StringUtils.hasText(businessKey)) {
            log.warn("取消已处理标记失败：参数不能为空 - logFileName: {}, businessKey: {}", logFileName, businessKey);
            return;
        }
        
        Map<String, Boolean> statusMap = loadProcessedStatus(logFileName);
        statusMap.remove(businessKey);
        
        // 更新缓存
        processedStatusCache.put(logFileName, statusMap);
        
        // 保存到文件
        saveProcessedStatus(logFileName, statusMap);
        
        log.info("取消已处理标记 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);
    }
    
    /**
     * 检查记录是否已处理
     */
    public boolean isProcessed(String logFileName, String businessKey) {
        if (!StringUtils.hasText(logFileName) || !StringUtils.hasText(businessKey)) {
            return false;
        }
        
        Map<String, Boolean> statusMap = loadProcessedStatus(logFileName);
        return statusMap.getOrDefault(businessKey, false);
    }
    
    /**
     * 获取指定日志文件的所有处理状态
     */
    public Map<String, Boolean> getAllProcessedStatus(String logFileName) {
        if (!StringUtils.hasText(logFileName)) {
            return new HashMap<>();
        }
        
        return new HashMap<>(loadProcessedStatus(logFileName));
    }
    
    /**
     * 清除指定日志文件的所有处理状态
     */
    public void clearAllProcessedStatus(String logFileName) {
        if (!StringUtils.hasText(logFileName)) {
            return;
        }
        
        // 清除缓存
        processedStatusCache.remove(logFileName);
        
        // 删除状态文件
        String statusFilePath = getStatusFilePath(logFileName);
        File statusFile = new File(statusFilePath);
        if (statusFile.exists()) {
            boolean deleted = statusFile.delete();
            if (deleted) {
                log.info("清除处理状态文件: {}", statusFilePath);
            } else {
                log.warn("清除处理状态文件失败: {}", statusFilePath);
            }
        }
    }
    
    /**
     * 获取处理状态统计信息
     */
    public Map<String, Object> getProcessedStatistics(String logFileName) {
        Map<String, Object> statistics = new HashMap<>();
        
        if (!StringUtils.hasText(logFileName)) {
            statistics.put("totalProcessed", 0);
            statistics.put("totalUnprocessed", 0);
            return statistics;
        }
        
        Map<String, Boolean> statusMap = loadProcessedStatus(logFileName);
        long processedCount = statusMap.values().stream().mapToLong(processed -> processed ? 1 : 0).sum();
        
        statistics.put("totalProcessed", processedCount);
        statistics.put("totalRecords", statusMap.size());
        
        return statistics;
    }
}
