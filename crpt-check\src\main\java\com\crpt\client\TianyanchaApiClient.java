package com.crpt.client;

import com.crpt.entity.TianyanchaResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;

/**
 * 天眼查API客户端
 */
@Component
public class TianyanchaApiClient {

    private static final Logger log = LoggerFactory.getLogger(TianyanchaApiClient.class);
    
    @Value("${tianyancha.api.base-url}")
    private String baseUrl;
    
    @Value("${tianyancha.api.token}")
    private String token;

    @Value("${tianyancha.api.search-endpoint}")
    private String searchEndpoint;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 根据社会信用代码查询企业信息
     * 使用统一的搜索接口
     */
    public TianyanchaResponse searchByCreditCode(String creditCode) {
        try {
            String url = baseUrl + searchEndpoint + "?word=" + URLEncoder.encode(creditCode, "UTF-8");
            log.debug("根据社会信用代码查询: {}", url);
            return executeRequest(url);
        } catch (Exception e) {
            log.error("根据社会信用代码查询企业信息失败: {}", e.getMessage(), e);
            return createErrorResponse("API调用失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业名称查询企业信息
     * 使用统一的搜索接口
     */
    public TianyanchaResponse searchByCompanyName(String companyName) {
        try {
            String url = baseUrl + searchEndpoint + "?word=" + URLEncoder.encode(companyName, "UTF-8");
            log.debug("根据企业名称查询: {}", url);
            return executeRequest(url);
        } catch (Exception e) {
            log.error("根据企业名称查询企业信息失败: {}", e.getMessage(), e);
            return createErrorResponse("API调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行HTTP请求
     */
    private TianyanchaResponse executeRequest(String url) throws IOException {
        log.info("=== 天眼查API调用详情 ===");
        log.info("请求URL: {}", url);
        log.info("Token: {}", token);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Authorization", token);
            httpGet.setHeader("Content-Type", "application/json");

            log.info("请求头: Authorization={}, Content-Type=application/json", token);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");

                log.info("HTTP状态码: {}", statusCode);
                log.info("响应内容: {}", responseBody);

                return objectMapper.readValue(responseBody, TianyanchaResponse.class);
            }
        }
    }
    
    /**
     * 创建错误响应
     */
    private TianyanchaResponse createErrorResponse(String errorMessage) {
        TianyanchaResponse response = new TianyanchaResponse();
        response.setError_code("500");
        response.setReason(errorMessage);
        return response;
    }
}
