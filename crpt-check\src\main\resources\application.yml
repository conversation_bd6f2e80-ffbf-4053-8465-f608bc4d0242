server:
  port: 8094
  servlet:
    context-path: /

spring:
  application:
    name: crpt-check

  # 数据源配置
  datasource:
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *******************************************************************************************************************************************
      username: root
      password: chiscdc
    oracle:
      driver-class-name: oracle.jdbc.OracleDriver
      url: ****************************************
      username: jlzw
      password: cybercdc

# 天眼查API配置
tianyancha:
  api:
    base-url: https://open.api.tianyancha.com
    search-endpoint: /services/open/search/2.0
    token:

# AI分析配置
ai:
  analysis:
    # 是否启用AI分析（设置为true启用）
    enabled: false
    # AI API地址（兼容OpenAI格式）
    # DeepSeek API 推荐使用: https://api.deepseek.com/chat/completions
    # 或者兼容格式: https://api.deepseek.com/v1/chat/completions
    # OpenAI: https://api.openai.com/v1/chat/completions
    api-url: https://api.deepseek.com/chat/completions
    # AI API密钥 - 请在 https://platform.deepseek.com/api_keys 申请
    api-key:
    # AI模型名称
    # deepseek-chat: DeepSeek-V3-0324 (推荐，性价比高)
    # deepseek-reasoner: DeepSeek-R1-0528 (推理能力强，价格较高)
    # gpt-3.5-turbo, gpt-4, gpt-4-turbo 等（如使用OpenAI）
    model: deepseek-chat
    # 请求超时时间（秒）
    timeout: 30

  # 禁用自动配置数据源
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

# 日志配置
logging:
  level:
    com.crpt: DEBUG
    org.springframework.jdbc: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/crpt-check.log

# 业务配置
crpt:
  check:
    # 分页大小
    page-size: 1000
    # 日志文件路径
    log-file-path: logs/match-results
    # API调用间隔（毫秒）
    api-interval: 100
