package com.crpt.service;

import com.crpt.client.TianyanchaApiClient;
import com.crpt.entity.MatchResult;
import com.crpt.entity.TianyanchaResponse;
import com.crpt.util.LogFileWriter;
import com.crpt.util.SqlParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 企业信息匹配服务
 */
@Service
public class CrptCheckService {

    private static final Logger log = LoggerFactory.getLogger(CrptCheckService.class);
    
    @Autowired
    private DatabaseService databaseService;
    
    @Autowired
    private TianyanchaApiClient tianyanchaApiClient;
    
    @Autowired
    private LogFileWriter logFileWriter;

    @Autowired
    private AiAnalysisService aiAnalysisService;

    @Value("${crpt.check.page-size}")
    private int pageSize;
    
    @Value("${crpt.check.api-interval}")
    private long apiInterval;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 执行企业信息匹配检查
     */
    public void executeCheck(String dbType, String tableName, String sql) {
        executeCheck(dbType, tableName, sql, false);
    }

    /**
     * 执行企业信息匹配检查（支持AI分析）
     */
    public void executeCheck(String dbType, String tableName, String sql, Boolean useAi) {
        executeCheck(dbType, tableName, sql, null, null, null, null, null, null, useAi);
    }

    /**
     * 执行企业信息匹配检查（完整参数版本）
     */
    public void executeCheck(String dbType, String tableName, String sql,
                           String creditCodeColumn, String companyNameColumn, String businessKeyColumn,
                           String delMarkColumn, String subOrgColumn, String updateSql, Boolean useAi) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行企业信息匹配检查 - 数据库类型: {}, 表名: {}, 使用AI: {}", dbType, tableName, useAi);

        // 解析SQL中的字段信息
        Map<String, Object> fieldInfo = SqlParser.parseFieldInfo(sql, tableName,
                creditCodeColumn, companyNameColumn, businessKeyColumn, delMarkColumn, subOrgColumn);

        log.info("SQL解析结果: {}", fieldInfo);

        // 初始化日志文件
        String logFileName = logFileWriter.initializeLogFile(dbType, tableName, startTime);
        if (logFileName == null) {
            log.error("初始化日志文件失败，任务终止");
            return;
        }

        // 记录配置参数到日志文件
        recordConfigurationToLog(logFileName, dbType, tableName, sql, creditCodeColumn, companyNameColumn,
                businessKeyColumn, delMarkColumn, subOrgColumn, updateSql, useAi, fieldInfo);

        // AI分析功能通过配置文件控制，无需临时设置

        long lastId = 0;
        int totalProcessed = 0;
        int successCount = 0;
        int failureCount = 0;

        try {
            while (true) {
                // 分页查询数据
                List<Map<String, Object>> dataList = databaseService.queryData(dbType, sql, lastId, pageSize);

                if (dataList.isEmpty()) {
                    log.info("数据查询完成，共处理 {} 条记录", totalProcessed);
                    break;
                }

                // 处理每条记录
                for (Map<String, Object> data : dataList) {
                    try {
                        MatchResult result = processRecord(logFileName, data, useAi, fieldInfo, updateSql);
                        totalProcessed++;

                        // 统计成功失败数量
                        if (result != null && result.getMatchTag() != null) {
                            if (result.getMatchTag() == 1) {
                                successCount++;
                            } else {
                                failureCount++;
                            }
                        }

                        // API调用间隔
                        if (apiInterval > 0) {
                            Thread.sleep(apiInterval);
                        }

                    } catch (Exception e) {
                        log.error("处理记录失败: {}", data, e);
                        failureCount++;
                    }
                }

                // 更新lastId，使用大小写不敏感的字段名匹配
                String idValueStr = getFieldValue(dataList.get(dataList.size() - 1), "ID");
                if (idValueStr != null) {
                    lastId = Long.parseLong(idValueStr);
                }

                log.info("已处理 {} 条记录，当前id: {}", totalProcessed, lastId);
            }

            // 完成日志记录
            logFileWriter.finalizeLogFile(logFileName, totalProcessed, successCount, failureCount);
            log.info("企业信息匹配检查完成 - 总处理: {}, 成功: {}, 失败: {}", totalProcessed, successCount, failureCount);

        } catch (Exception e) {
            log.error("执行企业信息匹配检查失败", e);
            // 即使出现异常也要完成日志记录
            logFileWriter.finalizeLogFile(logFileName, totalProcessed, successCount, failureCount);
            throw new RuntimeException("执行企业信息匹配检查失败: " + e.getMessage(), e);
        } finally {
            // AI分析功能通过配置文件控制，无需清除临时配置
        }
    }

    /**
     * 记录配置参数到日志文件
     */
    private void recordConfigurationToLog(String logFileName, String dbType, String tableName, String sql,
                                        String creditCodeColumn, String companyNameColumn, String businessKeyColumn,
                                        String delMarkColumn, String subOrgColumn, String updateSql, Boolean useAi,
                                        Map<String, Object> fieldInfo) {
        try {
            // 构建配置信息
            Map<String, Object> configInfo = new HashMap<>();
            configInfo.put("configType", "MATCH_CHECK_CONFIG");
            configInfo.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 基本配置
            configInfo.put("databaseType", dbType);
            configInfo.put("tableName", tableName);
            configInfo.put("useAi", useAi);

            // SQL配置
            configInfo.put("querySql", sql);
            configInfo.put("updateSql", updateSql);

            // 字段配置
            configInfo.put("creditCodeColumn", creditCodeColumn);
            configInfo.put("companyNameColumn", companyNameColumn);
            configInfo.put("businessKeyColumn", businessKeyColumn);
            configInfo.put("delMarkColumn", delMarkColumn);
            configInfo.put("subOrgColumn", subOrgColumn);

            // 解析后的字段信息
            if (fieldInfo != null) {
                configInfo.put("parsedFieldInfo", fieldInfo);
            }

            // API配置 - 确保类型转换正确
            configInfo.put("apiInterval", (int) apiInterval);
            configInfo.put("pageSize", pageSize);

            // 将配置信息写入日志
            logFileWriter.writeConfigurationInfo(logFileName, configInfo);
            log.info("配置参数已记录到日志文件: {}", logFileName);

        } catch (Exception e) {
            log.warn("记录配置参数到日志失败: {}", e.getMessage());
        }
    }

    /**
     * 处理单条记录
     */
    private MatchResult processRecord(String logFileName, Map<String, Object> data, Boolean useAi) throws Exception {
        return processRecord(logFileName, data, useAi, null, null);
    }

    /**
     * 处理单条记录（完整参数版本）
     */
    private MatchResult processRecord(String logFileName, Map<String, Object> data, Boolean useAi,
                                    Map<String, Object> fieldInfo, String updateSql) throws Exception {
        // 提取数据字段，使用别名从查询结果中获取数据
        // 从fieldInfo中获取用于数据提取的别名
        String businessKeyAlias = fieldInfo != null ? (String) fieldInfo.get("businessKeyColumn") : null;
        String companyNameAlias = fieldInfo != null ? (String) fieldInfo.get("companyNameColumn") : null;
        String creditCodeAlias = fieldInfo != null ? (String) fieldInfo.get("creditCodeColumn") : null;
        String delMarkAlias = fieldInfo != null ? (String) fieldInfo.get("delMarkColumn") : null;
        String subOrgAlias = fieldInfo != null ? (String) fieldInfo.get("subOrgColumn") : null;

        // 获取用于更新SQL的真实列名
        String realBusinessKeyColumn = fieldInfo != null ? (String) fieldInfo.get("realBusinessKeyColumn") : null;
        String realCompanyNameColumn = fieldInfo != null ? (String) fieldInfo.get("realCompanyNameColumn") : null;
        String realCreditCodeColumn = fieldInfo != null ? (String) fieldInfo.get("realCreditCodeColumn") : null;

        log.debug("数据提取别名 - businessKeyAlias: {}, companyNameAlias: {}, creditCodeAlias: {}",
                businessKeyAlias, companyNameAlias, creditCodeAlias);
        log.debug("更新SQL真实列名 - realBusinessKeyColumn: {}, realCompanyNameColumn: {}, realCreditCodeColumn: {}",
                realBusinessKeyColumn, realCompanyNameColumn, realCreditCodeColumn);
        log.debug("数据记录的实际字段: {}", data.keySet());

        String businessKey = getFieldValueByColumn(data, businessKeyAlias, "ID");
        String companyName = getFieldValueByColumn(data, companyNameAlias, "NAME");
        String creditCode = getFieldValueByColumn(data, creditCodeAlias, "CODE");
        String delMark = getFieldValueByColumn(data, delMarkAlias, "DEL_MARK");
        String subOrg = getFieldValueByColumn(data, subOrgAlias, "IF_SUB_ORG");

        log.debug("提取的字段值 - businessKey: {}, companyName: {}, creditCode: {}",
                businessKey, companyName, creditCode);

        if (!StringUtils.hasText(creditCode) || !StringUtils.hasText(companyName)) {
            log.warn("记录数据不完整，跳过处理 - 数据: {}, 提取结果: creditCode={}, companyName={}, businessKey={}",
                    data, creditCode, companyName, businessKey);
            log.warn("字段映射配置: businessKeyAlias={}, companyNameAlias={}, creditCodeAlias={}",
                    businessKeyAlias, companyNameAlias, creditCodeAlias);
            return null;
        }
        
        log.debug("处理记录 - 业务主键: {}, 单位名称: {}, 社会信用代码: {}", businessKey, companyName, creditCode);
        
        MatchResult matchResult = new MatchResult();
        matchResult.setBusinessKey(businessKey);
        matchResult.setOriginalCompanyName(companyName);
        matchResult.setOriginalCreditCode(creditCode);
        matchResult.setOriginalDelMark(delMark);
        matchResult.setOriginalSubOrg(subOrg);
        matchResult.setProcessTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 注意：parsed相关字段和updateSql不再存储在每个单位的JSON中，
        // 这些信息已经存储在配置信息中，避免重复存储
        
        // 执行匹配逻辑
        executeMatchLogic(matchResult);

        // 如果启用AI分析且匹配失败，进行AI分析
        if (useAi != null && useAi && matchResult.getMatchTag() != null && matchResult.getMatchTag() != 1) {
            try {
                String aiAnalysis = aiAnalysisService.analyzeMatchFailure(
                    matchResult.getOriginalCompanyName(),
                    matchResult.getOriginalCreditCode(),
                    matchResult.getCreditCodeMatchJson(),
                    matchResult.getCompanyNameMatchJson()
                );
                matchResult.setAiAnalysis(aiAnalysis);
                log.debug("AI分析完成 - 业务主键: {}, 分析结果: {}", businessKey, aiAnalysis);
            } catch (Exception e) {
                log.warn("AI分析失败 - 业务主键: {}, 错误: {}", businessKey, e.getMessage());
            }
        }

        // 写入日志文件
        logFileWriter.writeMatchResult(logFileName, matchResult);

        return matchResult;
    }

    /**
     * 执行匹配逻辑
     */
    private void executeMatchLogic(MatchResult matchResult) throws Exception {
        String originalCreditCode = matchResult.getOriginalCreditCode();
        String originalCompanyName = matchResult.getOriginalCompanyName();

        // 1. 先根据社会信用代码调用天眼查接口
        TianyanchaResponse creditCodeResponse = tianyanchaApiClient.searchByCreditCode(originalCreditCode);
        matchResult.setCreditCodeMatchJson(objectMapper.writeValueAsString(creditCodeResponse));

        if (hasValidData(creditCodeResponse)) {
            // 1.1 若返回有数据
            TianyanchaResponse.CompanyInfo creditCodeMatch = findMatchingCompany(creditCodeResponse, originalCreditCode);

            if (isCompanyMatch(creditCodeMatch, originalCreditCode, originalCompanyName)) {
                // 1.1.1 返回的单位名称和社会信用代码都匹配则标记【match_tag=1】
                matchResult.setMatchTag(1);
                return;
            } else {
                // 1.1.2 不完全匹配，再根据单位名称调用天眼查接口
                TianyanchaResponse nameResponse = tianyanchaApiClient.searchByCompanyName(originalCompanyName);
                matchResult.setCompanyNameMatchJson(objectMapper.writeValueAsString(nameResponse));

                if (!hasValidData(nameResponse)) {
                    // 1.1.2.a 若返回无数据则标记【match_tag=2】（社会信用代码能查到，单位名称查不到）
                    matchResult.setMatchTag(2);
                } else {
                    TianyanchaResponse.CompanyInfo nameMatch = findMatchingCompany(nameResponse, originalCompanyName);
                    if (isCompanyMatch(nameMatch, originalCreditCode, originalCompanyName)) {
                        // 1.1.2.b 若返回有数据且完全匹配则标记【match_tag=1】
                        matchResult.setMatchTag(1);
                    } else if (nameMatch != null && originalCompanyName.equals(nameMatch.getName())) {
                        // 1.1.2.c 单位名称匹配但社会信用代码不匹配则标记【match_tag=5】
                        matchResult.setMatchTag(5);
                    } else {
                        // 1.1.2.d 都不匹配则标记【match_tag=2】
                        matchResult.setMatchTag(2);
                    }
                }
            }
        } else {
            // 1.2 若根据社会信用代码返回无数据，再根据单位名称调用天眼查接口
            TianyanchaResponse nameResponse = tianyanchaApiClient.searchByCompanyName(originalCompanyName);
            matchResult.setCompanyNameMatchJson(objectMapper.writeValueAsString(nameResponse));

            if (!hasValidData(nameResponse)) {
                // 1.2.1 若返回无数据则标记【match_tag=4】（都查不到）
                matchResult.setMatchTag(4);
            } else {
                TianyanchaResponse.CompanyInfo nameMatch = findMatchingCompany(nameResponse, originalCompanyName);
                if (isCompanyMatch(nameMatch, originalCreditCode, originalCompanyName)) {
                    // 1.2.2 若返回有数据且完全匹配则标记【match_tag=1】
                    matchResult.setMatchTag(1);
                } else if (nameMatch != null && originalCompanyName.equals(nameMatch.getName())) {
                    // 1.2.3 若单位名称匹配但社会信用代码不匹配则标记【match_tag=3】
                    matchResult.setMatchTag(3);
                } else {
                    // 1.2.4 若都不匹配则标记【match_tag=4】
                    matchResult.setMatchTag(4);
                }
            }
        }
    }

    /**
     * 检查响应是否有有效数据
     * 适配搜索接口的响应格式
     */
    private boolean hasValidData(TianyanchaResponse response) {
        return response != null
            && (response.getError_code() == null || "0".equals(response.getError_code()) || "200".equals(response.getError_code()))
            && response.getResult() != null
            && response.getResult().getItems() != null
            && !response.getResult().getItems().isEmpty();
    }

    /**
     * 查找匹配的企业信息
     * 优化匹配逻辑，支持精确匹配和模糊匹配
     */
    private TianyanchaResponse.CompanyInfo findMatchingCompany(TianyanchaResponse response, String keyword) {
        if (!hasValidData(response)) {
            return null;
        }

        List<TianyanchaResponse.CompanyInfo> items = response.getResult().getItems();

        // 1. 优先精确匹配
        for (TianyanchaResponse.CompanyInfo item : items) {
            // 精确匹配社会信用代码
            if (keyword != null && (keyword.equals(item.getCreditCode()) || keyword.equals(item.getRegNumber()))) {
                return item;
            }
            // 精确匹配企业名称
            if (keyword != null && keyword.equals(item.getName())) {
                return item;
            }
        }

        // 2. 如果没有精确匹配，返回第一个结果
        return items.get(0);
    }

    /**
     * 检查企业信息是否匹配
     */
    private boolean isCompanyMatch(TianyanchaResponse.CompanyInfo company, String creditCode, String companyName) {
        if (company == null) {
            return false;
        }

        // 检查社会信用代码匹配
        boolean creditCodeMatch = creditCode != null &&
            (creditCode.equals(company.getCreditCode()) || creditCode.equals(company.getRegNumber()));

        // 检查企业名称匹配
        boolean nameMatch = companyName != null && companyName.equals(company.getName());

        return creditCodeMatch && nameMatch;
    }

    /**
     * 大小写不敏感地获取字段值
     * 支持用户输入小写SQL和数据库返回小写列名的情况
     */
    private String getFieldValue(Map<String, Object> data, String fieldName) {
        // 1. 先尝试直接匹配（大写）
        Object value = data.get(fieldName);
        if (value != null) {
            return String.valueOf(value);
        }

        // 2. 尝试小写匹配
        value = data.get(fieldName.toLowerCase());
        if (value != null) {
            return String.valueOf(value);
        }

        // 3. 遍历所有键，进行大小写不敏感匹配
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (fieldName.equalsIgnoreCase(entry.getKey())) {
                Object val = entry.getValue();
                return val != null ? String.valueOf(val) : null;
            }
        }

        return null;
    }

    /**
     * 根据指定的列名获取字段值，如果列名为空则使用默认字段名
     * 优化版本：查询结果的列名就是SQL中的别名，直接使用指定的列名
     */
    private String getFieldValueByColumn(Map<String, Object> data, String columnName, String defaultFieldName) {
        String result = null;

        if (StringUtils.hasText(columnName)) {
            // 使用指定的列名（应该是SQL中的别名）
            result = getFieldValue(data, columnName);
            log.debug("使用指定列名 {} 获取字段值: {}", columnName, result);
        } else {
            // 使用默认字段名
            result = getFieldValue(data, defaultFieldName);
            log.debug("使用默认字段名 {} 获取字段值: {}", defaultFieldName, result);
        }

        // 如果没有找到值，记录详细信息用于调试
        if (result == null) {
            log.warn("未能获取字段值 - 指定列名: {}, 默认字段名: {}, 数据中的字段: {}",
                    columnName, defaultFieldName, data.keySet());
        }

        return result;
    }

    /**
     * 执行更新SQL语句
     */
    public int executeUpdateSql(String dbType, String updateSql) {
        log.info("执行更新SQL - 数据库类型: {}, SQL: {}", dbType, updateSql);
        return databaseService.executeUpdate(dbType, updateSql);
    }
}
