package com.crpt.service;

import com.crpt.config.AiAnalysisConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * AI分析服务
 */
@Service
public class AiAnalysisService {

    private static final Logger log = LoggerFactory.getLogger(AiAnalysisService.class);

    @Autowired
    private AiAnalysisConfig aiConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();



    /**
     * 验证API地址格式
     */
    private boolean isValidApiUrl(String apiUrl) {
        if (!StringUtils.hasText(apiUrl)) {
            return false;
        }

        try {
            // 检查是否为有效的URI
            java.net.URI uri = java.net.URI.create(apiUrl);

            // 检查协议
            String scheme = uri.getScheme();
            if (!"http".equals(scheme) && !"https".equals(scheme)) {
                return false;
            }

            // 检查主机名
            String host = uri.getHost();
            if (!StringUtils.hasText(host)) {
                return false;
            }

            // 检查路径（应该包含chat/completions或类似的端点）
            String path = uri.getPath();
            if (StringUtils.hasText(path)) {
                String lowerPath = path.toLowerCase();
                // 常见的AI API端点模式
                if (lowerPath.contains("chat/completions") ||
                    lowerPath.contains("completions") ||
                    lowerPath.contains("messages") ||
                    lowerPath.contains("generate")) {
                    return true;
                }

                // 如果路径不包含已知端点，给出警告但不阻止
                log.warn("API地址路径可能不正确，请确认端点支持OpenAI格式: {}", path);
            }

            return true;

        } catch (Exception e) {
            log.warn("API地址格式验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 分析匹配失败原因
     */
    public String analyzeMatchFailure(String originalCompanyName, String originalCreditCode,
                                    String creditCodeApiResponse, String companyNameApiResponse) {

        // 检查是否启用AI分析
        if (!aiConfig.isEnabled()) {
            log.debug("AI分析功能未启用");
            return null;
        }

        // 检查必要参数
        if (!StringUtils.hasText(aiConfig.getApiKey()) || !StringUtils.hasText(aiConfig.getApiUrl())) {
            log.warn("AI分析配置不完整，缺少API密钥或地址");
            return null;
        }

        // 验证API地址格式
        String apiUrl = aiConfig.getApiUrl();
        if (!isValidApiUrl(apiUrl)) {
            log.warn("AI API地址格式不正确: {}", apiUrl);
            return "API地址格式不正确，请检查配置";
        }

        // 记录AI分析请求信息
        log.info("开始AI分析 - API地址: {}, 模型: {}, 企业: {}",
                apiUrl, aiConfig.getModel(), originalCompanyName);

        try {
            // 构建AI分析请求
            String prompt = buildAnalysisPrompt(originalCompanyName, originalCreditCode, 
                                              creditCodeApiResponse, companyNameApiResponse);
            
            // 调用AI API
            String response = callAiApi(prompt);
            
            if (StringUtils.hasText(response)) {
                log.debug("AI分析完成: {}", response);
                return response.trim();
            }
            
        } catch (Exception e) {
            log.error("AI分析失败", e);
        }
        
        return null;
    }

    /**
     * 构建AI分析提示词
     */
    private String buildAnalysisPrompt(String originalCompanyName, String originalCreditCode,
                                     String creditCodeApiResponse, String companyNameApiResponse) {

        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个专业的企业信息分析专家。请分析企业信息匹配失败的原因，并按照指定的Markdown格式返回结果。\n\n");

        prompt.append("## 输入数据\n\n");
        prompt.append("### 原始企业信息\n");
        prompt.append("- **企业名称**：").append(originalCompanyName != null ? originalCompanyName : "无").append("\n");
        prompt.append("- **社会信用代码**：").append(originalCreditCode != null ? originalCreditCode : "无").append("\n\n");

        prompt.append("### 通过社会信用代码查询的API返回结果\n");
        prompt.append("```json\n");
        prompt.append(creditCodeApiResponse != null ? creditCodeApiResponse : "无数据");
        prompt.append("\n```\n\n");

        prompt.append("### 通过企业名称查询的API返回结果\n");
        prompt.append("```json\n");
        prompt.append(companyNameApiResponse != null ? companyNameApiResponse : "无数据");
        prompt.append("\n```\n\n");

        prompt.append("## 分析要求\n");
        prompt.append("请严格按照以下Markdown格式返回分析结果，不要添加任何其他内容：\n\n");

        prompt.append("```markdown\n");

        prompt.append("## 🔍 可能匹配的信息\n");
        prompt.append("### 通过[社会信用代码或企业名称]查询到的企业\n");
        prompt.append("- **企业名称**：[查询到的企业名称]\n");
        prompt.append("- **社会信用代码**：[查询到的社会信用代码]\n");
        prompt.append("## 💡 原因\n");
        prompt.append("[简洁明确地说明匹配失败的主要原因，如：企业名称不一致、社会信用代码错误、企业已注销等]\n");
        prompt.append("```\n\n");

        prompt.append("**注意事项：**\n");
        prompt.append("1. 如果API返回均无数据或错误，只需要展示原因\n");
        prompt.append("2. 主要原因用差异标注格式 ~~原始信息差异部分~~ → **新信息差异部分** 突出显示不一致的地方(例如：社会信用代码最后一位不一致 91320585608268400~~9~~ → 91320585608268400**P**)\n");
        prompt.append("4. 原因分析要具体明确，避免模糊表述\n");
        prompt.append("5. 严格按照上述Markdown格式返回，不要添加额外的说明文字\n\n");

        prompt.append("请开始分析：");

        return prompt.toString();
    }

    /**
     * 调用AI API
     */
    private String callAiApi(String prompt) throws Exception {
        
        // 构建请求体
        String requestBody = buildRequestBody(prompt);

        // 记录请求信息（不记录完整请求体，避免日志过长）
        log.debug("AI API请求 - URL: {}, 模型: {}, 超时: {}秒",
                aiConfig.getApiUrl(), aiConfig.getModel(), aiConfig.getTimeout());

        // 配置HTTP客户端
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(aiConfig.getTimeout() * 1000)
                .setSocketTimeout(aiConfig.getTimeout() * 1000)
                .build();

        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build()) {

            HttpPost httpPost = new HttpPost(aiConfig.getApiUrl());
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", "Bearer " + aiConfig.getApiKey());
            
            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                if (statusCode == 200) {
                    return parseAiResponse(responseBody);
                } else {
                    handleApiError(statusCode, responseBody);
                    return null;
                }
            }
        }
    }

    /**
     * 构建AI API请求体
     */
    private String buildRequestBody(String prompt) throws Exception {

        String escapedPrompt = prompt.replace("\"", "\\\"").replace("\n", "\\n");

        // 根据模型类型调整参数
        String model = aiConfig.getModel();
        int maxTokens = 800; // 增加token数量以支持Markdown格式输出
        double temperature = 0.2; // 降低温度以获得更稳定的格式输出

        // DeepSeek-reasoner 模型需要更多 tokens 用于推理
        if ("deepseek-reasoner".equals(model)) {
            maxTokens = 1200; // 推理模型需要更多tokens
            temperature = 0.1; // 推理模型使用更低的温度
        }

        String requestJson = String.format(
            "{" +
                "\"model\": \"%s\"," +
                "\"messages\": [" +
                    "{" +
                        "\"role\": \"system\"," +
                        "\"content\": \"你是一个专业的企业信息分析专家，擅长分析企业数据匹配问题。请严格按照用户要求的Markdown格式返回分析结果，不要添加任何额外的解释或格式。确保输出格式完全符合要求。\"" +
                    "}," +
                    "{" +
                        "\"role\": \"user\"," +
                        "\"content\": \"%s\"" +
                    "}" +
                "]," +
                "\"max_tokens\": %d," +
                "\"temperature\": %.1f," +
                "\"stream\": false" +
            "}",
            model,
            escapedPrompt,
            maxTokens,
            temperature
        );

        return requestJson;
    }

    /**
     * 处理API错误
     */
    private void handleApiError(int statusCode, String responseBody) {
        String errorMessage = "AI API调用失败";
        String suggestion = "";

        switch (statusCode) {
            case 400:
                errorMessage = "AI API请求参数错误";
                suggestion = "请检查模型名称和请求格式是否正确";
                break;
            case 401:
                errorMessage = "AI API认证失败";
                suggestion = "请检查API密钥是否正确";
                break;
            case 403:
                errorMessage = "AI API访问被拒绝";
                suggestion = "请检查API密钥权限或账户余额";
                break;
            case 404:
                errorMessage = "AI API端点未找到";
                suggestion = "请检查API地址是否正确。DeepSeek API端点：/chat/completions 或 /v1/chat/completions";
                break;
            case 429:
                errorMessage = "AI API请求频率超限";
                suggestion = "请稍后重试或检查API配额";
                break;
            case 500:
                errorMessage = "AI API服务器内部错误";
                suggestion = "服务商服务异常，请稍后重试";
                break;
            case 502:
            case 503:
            case 504:
                errorMessage = "AI API服务不可用";
                suggestion = "服务商服务暂时不可用，请稍后重试";
                break;
            default:
                errorMessage = "AI API调用失败，未知错误";
                suggestion = "请检查网络连接和API配置";
        }

        // 尝试解析错误响应中的详细信息
        String detailError = extractErrorFromResponse(responseBody);
        if (StringUtils.hasText(detailError)) {
            errorMessage += "，详细错误: " + detailError;
        }

        log.error("{} (状态码: {}) - {} - 响应: {}", errorMessage, statusCode, suggestion, responseBody);
    }

    /**
     * 从响应中提取错误信息
     */
    private String extractErrorFromResponse(String responseBody) {
        try {
            JsonNode rootNode = objectMapper.readTree(responseBody);

            // 尝试不同的错误字段
            String[] errorFields = {"error_msg", "error", "message", "detail", "error_description"};

            for (String field : errorFields) {
                JsonNode errorNode = rootNode.get(field);
                if (errorNode != null && !errorNode.isNull()) {
                    if (errorNode.isTextual()) {
                        return errorNode.asText();
                    } else if (errorNode.isObject()) {
                        JsonNode messageNode = errorNode.get("message");
                        if (messageNode != null) {
                            return messageNode.asText();
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug("解析错误响应失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 解析AI API响应
     */
    private String parseAiResponse(String responseBody) throws Exception {
        
        JsonNode rootNode = objectMapper.readTree(responseBody);
        JsonNode choicesNode = rootNode.get("choices");
        
        if (choicesNode != null && choicesNode.isArray() && choicesNode.size() > 0) {
            JsonNode firstChoice = choicesNode.get(0);
            JsonNode messageNode = firstChoice.get("message");
            
            if (messageNode != null) {
                JsonNode contentNode = messageNode.get("content");
                if (contentNode != null) {
                    return contentNode.asText();
                }
            }
        }
        
        log.warn("无法解析AI API响应: {}", responseBody);
        return null;
    }
}
