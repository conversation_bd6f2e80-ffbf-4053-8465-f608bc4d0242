<!DOCTYPE html>
<html lang="zh-CN" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库 DDL 转换工具 v1.2</title>
    <script src="assets/js/tailwind.min.js"></script>
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .code-editor {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        /* 隐藏滚动条但保持滚动功能 */
        .code-editor::-webkit-scrollbar {
            width: 8px;
        }

        .code-editor::-webkit-scrollbar-track {
            background: transparent;
        }

        .code-editor::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.5);
            border-radius: 4px;
        }

        .code-editor::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.7);
        }

        /* 隐藏滚动条的上下箭头 */
        .code-editor::-webkit-scrollbar-button {
            display: none;
        }

        /* 确保页面不出现滚动条 */
        html, body {
            overflow: hidden;
            height: 100vh;
        }
    </style>
</head>
<body class="h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300 flex flex-col">
<!-- Header -->
<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
    <div class="w-full px-2 sm:px-3 lg:px-4">
        <div class="flex justify-between items-center h-16">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-database text-white text-sm"></i>
                </div>
                <h1 class="text-xl font-semibold text-gray-900 dark:text-white">数据库 DDL 转换工具</h1>
                <!-- 转换规则说明 -->
                <div class="relative inline-block ml-4">
                    <button
                            id="rulesTooltipBtn"
                            class="flex items-center px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-all duration-200 transform hover:scale-105"
                            onmouseover="showRulesTooltip()"
                            onmouseout="hideRulesTooltip()"
                    >
                        <i class="fas fa-info-circle mr-1"></i>
                        转换规则
                    </button>

                    <!-- Tooltip Content -->
                    <div
                            id="rulesTooltip"
                            class="absolute top-full left-0 mt-2 w-[800px] max-w-[calc(100vw-2rem)] bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transition-all duration-300 z-50"
                    >
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">基本规则</h4>
                                    <ul id="basicRules" class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                                        <li class="flex items-start">
                                            <i class="fas fa-check text-green-500 mr-2 mt-0.5"></i>
                                            表名和列名全部转换为小写
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check text-green-500 mr-2 mt-0.5"></i>
                                            自动添加表名前缀（可自定义）
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check text-green-500 mr-2 mt-0.5"></i>
                                            <span id="primaryKeyRule">添加主键列 rid (BIGINT NOT NULL)</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check text-green-500 mr-2 mt-0.5"></i>
                                            <span id="systemColumnsRule">添加系统时间戳和删除标记列</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-cog text-blue-500 mr-2 mt-0.5"></i>
                                            可选择是否包含NOT NULL约束（不影响固定列）
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-2 mt-0.5"></i>
                                            原表中与固定列同名的列将被移除
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-comment text-blue-500 mr-2 mt-0.5"></i>
                                            自动同步表和列的注释信息（COMMENT）
                                        </li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">类型映射</h4>
                                    <div class="overflow-x-auto">
                                        <table id="typeMappingTable" class="w-full text-sm">
                                            <thead>
                                            <tr class="border-b border-gray-200 dark:border-gray-700">
                                                <th id="sourceDbHeader" class="text-left py-2 text-gray-900 dark:text-white">Oracle</th>
                                                <th class="text-left py-2 text-gray-900 dark:text-white">Doris</th>
                                            </tr>
                                            </thead>
                                            <tbody id="typeMappingBody" class="text-gray-600 dark:text-gray-300 text-xs">
                                            <tr><td class="py-1">NUMBER</td><td class="py-1">DECIMAL/BIGINT</td></tr>
                                            <tr><td class="py-1">INTEGER</td><td class="py-1">BIGINT</td></tr>
                                            <tr><td class="py-1">VARCHAR2/NVARCHAR2</td><td class="py-1">VARCHAR(65533)</td></tr>
                                            <tr><td class="py-1">CHAR/NCHAR</td><td class="py-1">CHAR</td></tr>
                                            <tr><td class="py-1">DATE/TIMESTAMP</td><td class="py-1">DATETIME</td></tr>
                                            <tr><td class="py-1">CLOB/NCLOB</td><td class="py-1">STRING</td></tr>
                                            <tr><td class="py-1">BLOB/RAW</td><td class="py-1">BINARY</td></tr>
                                            <tr><td class="py-1">BINARY_FLOAT</td><td class="py-1">FLOAT</td></tr>
                                            <tr><td class="py-1">BINARY_DOUBLE</td><td class="py-1">DOUBLE</td></tr>
                                            <tr><td class="py-1">ROWID</td><td class="py-1">VARCHAR(65533)</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Tooltip Arrow -->
                        <div class="absolute bottom-full left-12 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white dark:border-b-gray-800"></div>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <!-- 主题切换 -->
                <button
                        id="themeToggle"
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 transform hover:scale-105"
                >
                    <i class="fas fa-sun text-yellow-500 dark:hidden"></i>
                    <i class="fas fa-moon text-blue-400 hidden dark:block"></i>
                </button>
            </div>
        </div>
    </div>
</header>

<!-- Main Content -->
<main class="flex-1 w-full px-2 sm:px-3 lg:px-4 py-8 overflow-hidden">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-2 h-full w-full">
        <!-- Source Database Input Panel -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden animate-fade-in flex flex-col h-full">
            <div id="inputHeader" class="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i id="inputIcon" class="fab fa-oracle text-white text-lg"></i>
                        <h2 id="inputTitle" class="text-lg font-semibold text-white">Oracle 建表语句</h2>
                    </div>
                    <div class="flex space-x-2">
                        <!-- 数据库切换按钮 -->
                        <button
                                id="sourceDbToggle"
                                class="px-3 py-1 text-white text-sm rounded-md transition-all duration-300 transform hover:scale-105"
                                style="background-color: rgba(255, 165, 0, 0.8);"
                        >
                            <i class="fas fa-exchange-alt mr-1"></i><span id="sourceDbText">Oracle</span>
                        </button>
                        <button
                                id="clearInput"
                                class="px-3 py-1 bg-white/20 hover:bg-white/30 text-white text-sm rounded-md transition-all duration-200 transform hover:scale-105"
                        >
                            <i class="fas fa-trash mr-1"></i>清空
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6 flex-1 flex flex-col">
                    <textarea
                            id="sourceInput"
                            placeholder="请输入建表语句..."
                            class="w-full flex-1 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white code-editor text-sm resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                    ></textarea>
            </div>
        </div>

        <!-- Doris Output Panel -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden animate-fade-in flex flex-col h-full">
            <div id="dorisHeader" class="bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i id="outputIcon" class="fas fa-database text-white text-lg"></i>
                        <h2 id="outputTitle" class="text-lg font-semibold text-white">Doris 建表语句</h2>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- 目标数据库切换按钮 -->
                        <button
                                id="targetDbToggle"
                                class="px-3 py-1 text-white text-sm rounded-md transition-all duration-300 transform hover:scale-105"
                                style="background-color: rgba(34, 197, 94, 0.8);"
                        >
                            <i class="fas fa-exchange-alt mr-1"></i><span id="targetDbText">Doris</span>
                        </button>
                        <!-- 版本切换按钮 -->
                        <button
                                id="versionToggle"
                                class="px-3 py-1 text-white text-sm rounded-md transition-all duration-300 transform hover:scale-105"
                                style="background-color: rgba(34, 197, 94, 0.8);"
                        >
                            <i class="fas fa-code-branch mr-1"></i><span id="versionText">RID版本</span>
                        </button>
                        <!-- 表名前缀设置 -->
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-white">前缀:</label>
                            <input
                                    type="text"
                                    id="tablePrefix"
                                    placeholder="stg_"
                                    class="px-2 py-1 text-xs border border-white/30 rounded-md bg-white/20 text-white placeholder-white/70 focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200 w-20"
                            >
                        </div>
                        <button
                                id="copyOutput"
                                class="px-3 py-1 bg-white/20 hover:bg-white/30 text-white text-sm rounded-md transition-all duration-200 transform hover:scale-105"
                        >
                            <i class="fas fa-copy mr-1"></i>复制
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6 flex-1 flex flex-col">
                    <textarea
                            id="dorisOutput"
                            readonly
                            placeholder="转换后的Doris建表语句将在这里显示..."
                            class="w-full flex-1 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white code-editor text-sm resize-none"
                    ></textarea>
            </div>
        </div>
    </div>


</main>

<!-- 隐藏的元素用于JavaScript兼容性 -->
<div style="display: none;">
    <select id="sourceDatabase">
        <option value="oracle">Oracle</option>
        <option value="mysql">MySQL</option>
    </select>
    <select id="targetDatabase">
        <option value="doris">Doris</option>
        <option value="mysql">MySQL</option>
    </select>
    <select id="versionType">
        <option value="rid">RID版本</option>
        <option value="id">ID版本</option>
    </select>
    <input type="checkbox" id="includeNotNull" checked>
</div>

<!-- Toast Notification -->
<div id="toast" class="fixed top-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-300 z-50 opacity-0 invisible" style="right: -300px;">
    <i class="fas fa-check mr-2"></i>
    <span id="toastMessage">操作成功</span>
</div>

<script>
    // 主题切换功能
    const themeToggle = document.getElementById('themeToggle');
    const html = document.documentElement;

    // 初始化主题
    if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        html.classList.add('dark');
    } else {
        html.classList.remove('dark');
    }

    themeToggle.addEventListener('click', () => {
        html.classList.toggle('dark');
        localStorage.theme = html.classList.contains('dark') ? 'dark' : 'light';
    });

    // Oracle到Doris类型映射
    const oracleTypeMapping = {
        'NUMBER': (precision, scale) => {
            // 首先检查是否包含星号，如果有则直接返回BIGINT
            if (precision === '*' || precision === undefined || precision === null) {
                return 'BIGINT';
            }

            // 转换为数字进行验证
            const numPrecision = parseInt(precision);
            const numScale = scale ? parseInt(scale) : 0;

            // 验证精度范围（Doris支持1-76）
            if (isNaN(numPrecision) || numPrecision <= 0 || numPrecision > 76) {
                return 'BIGINT';
            }

            // 如果精度过大或者没有小数位，使用BIGINT
            if (numPrecision > 18 && numScale === 0) {
                return 'BIGINT';
            }

            // 返回有效的DECIMAL类型
            return `DECIMAL(${numPrecision},${numScale})`;
        },
        'VARCHAR': () => 'VARCHAR(65533)',
        'NVARCHAR': () => 'VARCHAR(65533)',
        'VARCHAR2': () => 'VARCHAR(65533)',
        'NVARCHAR2': () => 'VARCHAR(65533)',
        'CHAR': () => 'CHAR(255)',
        'NCHAR': () => 'CHAR(255)',
        'DATE': () => 'DATETIME',
        'TIMESTAMP': () => 'DATETIME(6)',
        'CLOB': () => 'STRING',
        'NCLOB': () => 'STRING',                             // Unicode大对象
        'BLOB': () => 'BINARY',
        'BFILE': () => 'VARCHAR(65533)',                     // 外部文件引用，存储路径
        'RAW': (size) => 'BINARY',                         // 二进制数据
        'LONG': () => 'STRING',                              // 长字符串（已废弃）
        'LONG RAW': () => 'BINARY',                        // 长二进制数据（已废弃）
        'ROWID': () => 'VARCHAR(65533)',                      // 行标识符
        'UROWID': (size) => 'VARCHAR(65533)',    // 通用行标识符
        'INTEGER': () => 'BIGINT',                         // Oracle INTEGER可能很大，使用BIGINT更安全
        'INT': () => 'BIGINT',                             // 同样使用BIGINT
        'SMALLINT': () => 'INT',                           // 小整数
        'FLOAT': () => 'DOUBLE',
        'DOUBLE': () => 'DOUBLE',
        'BINARY_FLOAT': () => 'FLOAT',                     // Oracle 32位浮点
        'BINARY_DOUBLE': () => 'DOUBLE',                   // Oracle 64位浮点
        'REAL': () => 'FLOAT'                              // 实数类型
    };

    // MySQL到Doris类型映射
    const mysqlTypeMapping = {
        'TINYINT': (size, unsigned) => unsigned ? 'SMALLINT' : 'TINYINT',
        'SMALLINT': (size, unsigned) => unsigned ? 'INT' : 'SMALLINT',
        'MEDIUMINT': (size, unsigned) => 'INT',
        'INT': (size, unsigned) => unsigned ? 'BIGINT' : 'INT',
        'INTEGER': (size, unsigned) => unsigned ? 'BIGINT' : 'INT',
        'BIGINT': (size, unsigned) => 'BIGINT',
        'DECIMAL': (precision, scale) => {
            if (!precision) return 'DECIMAL(10,0)';
            const numPrecision = parseInt(precision);
            const numScale = scale ? parseInt(scale) : 0;
            if (numPrecision > 76) return 'BIGINT';
            return `DECIMAL(${numPrecision},${numScale})`;
        },
        'NUMERIC': (precision, scale) => {
            if (!precision) return 'DECIMAL(10,0)';
            const numPrecision = parseInt(precision);
            const numScale = scale ? parseInt(scale) : 0;
            if (numPrecision > 76) return 'BIGINT';
            return `DECIMAL(${numPrecision},${numScale})`;
        },
        'FLOAT': (precision, scale) => 'FLOAT',
        'DOUBLE': (precision, scale) => 'DOUBLE',
        'REAL': () => 'DOUBLE',
        'BIT': (size) => 'BOOLEAN',
        'BOOLEAN': () => 'BOOLEAN',
        'BOOL': () => 'BOOLEAN',
        'CHAR': (size) => 'CHAR(255)',
        'VARCHAR': (size) => 'VARCHAR(65533)',
        'BINARY': (size) => 'BINARY',
        'VARBINARY': (size) => 'BINARY',
        'TINYBLOB': () => 'BINARY',
        'BLOB': () => 'BINARY',
        'MEDIUMBLOB': () => 'BINARY',
        'LONGBLOB': () => 'BINARY',
        'TINYTEXT': () => 'STRING',
        'TEXT': () => 'STRING',
        'STRING': () => 'STRING',
        'MEDIUMTEXT': () => 'STRING',
        'LONGTEXT': () => 'STRING',
        'ENUM': () => 'VARCHAR(65533)',
        'SET': () => 'VARCHAR(65533)',
        'DATE': () => 'DATE',
        'TIME': () => 'VARCHAR(65533)',
        'DATETIME': (precision) => 'DATETIME(6)',
        'TIMESTAMP': (precision) => 'DATETIME(6)',
        'YEAR': () => 'SMALLINT',
        'JSON': () => 'STRING',
        'GEOMETRY': () => 'STRING',
        'POINT': () => 'STRING',
        'LINESTRING': () => 'STRING',
        'POLYGON': () => 'STRING',
        'MULTIPOINT': () => 'STRING',
        'MULTILINESTRING': () => 'STRING',
        'MULTIPOLYGON': () => 'STRING',
        'GEOMETRYCOLLECTION': () => 'STRING'
    };

    // Oracle到MySQL类型映射
    const oracleToMysqlMapping = {
        'NUMBER': (precision, scale) => {
            if (!precision || precision === '*') return 'BIGINT';
            const p = parseInt(precision);
            const s = scale ? parseInt(scale) : 0;
            if (s === 0) {
                if (p <= 3) return 'TINYINT';
                if (p <= 5) return 'SMALLINT';
                if (p <= 10) return 'INT';
                return 'BIGINT';
            }
            return `DECIMAL(${p},${s})`;
        },
        'VARCHAR2': (size) => {
            if (!size) return 'VARCHAR(12000)'; // Oracle VARCHAR2最大4000字节，考虑3倍字符差异
            const originalSize = parseInt(size);
            const adjustedSize = Math.min(originalSize * 3, 65535); // MySQL VARCHAR最大65535
            return `VARCHAR(${adjustedSize})`;
        },
        'NVARCHAR2': (size) => {
            if (!size) return 'VARCHAR(12000)'; // Oracle NVARCHAR2最大4000字符，考虑3倍字符差异
            const originalSize = parseInt(size);
            const adjustedSize = Math.min(originalSize * 3, 65535);
            return `VARCHAR(${adjustedSize})`;
        },
        'VARCHAR': (size) => {
            if (!size) return 'VARCHAR(12000)';
            const originalSize = parseInt(size);
            const adjustedSize = Math.min(originalSize * 3, 65535);
            return `VARCHAR(${adjustedSize})`;
        },
        'NVARCHAR': (size) => {
            if (!size) return 'VARCHAR(12000)';
            const originalSize = parseInt(size);
            const adjustedSize = Math.min(originalSize * 3, 65535);
            return `VARCHAR(${adjustedSize})`;
        },
        'CHAR': (size) => {
            if (!size) return 'CHAR(255)'; // Oracle CHAR最大2000字节，但MySQL CHAR最大255
            const originalSize = parseInt(size);
            const adjustedSize = Math.min(originalSize * 3, 255); // MySQL CHAR最大255
            return `CHAR(${adjustedSize})`;
        },
        'NCHAR': (size) => {
            if (!size) return 'CHAR(255)';
            const originalSize = parseInt(size);
            const adjustedSize = Math.min(originalSize * 3, 255);
            return `CHAR(${adjustedSize})`;
        },
        'DATE': () => 'DATETIME',
        'TIMESTAMP': () => 'DATETIME(6)',
        'CLOB': () => 'LONGTEXT',
        'NCLOB': () => 'LONGTEXT',
        'BLOB': () => 'LONGBLOB',
        'RAW': (size) => {
            const maxSize = size ? Math.min(parseInt(size), 65535) : 255;
            return `VARBINARY(${maxSize})`;
        },
        'INTEGER': () => 'INT',
        'INT': () => 'INT',
        'SMALLINT': () => 'SMALLINT',
        'FLOAT': () => 'FLOAT',
        'BINARY_FLOAT': () => 'FLOAT',
        'BINARY_DOUBLE': () => 'DOUBLE',
        'DOUBLE': () => 'DOUBLE',
        'REAL': () => 'FLOAT',
        'ROWID': () => 'VARCHAR(18)',
        'UROWID': () => 'VARCHAR(18)',
        'LONG': () => 'LONGTEXT',
        'LONG RAW': () => 'LONGBLOB',
        'BFILE': () => 'VARCHAR(255)'
    };

    // 获取当前类型映射
    function getCurrentTypeMapping() {
        const sourceDb = document.getElementById('sourceDatabase').value;
        return sourceDb === 'mysql' ? mysqlTypeMapping : oracleTypeMapping;
    }

    // 根据目标数据库转换Oracle类型
    function convertOracleTypeToTarget(baseType, params, targetDb = 'doris') {
        if (targetDb === 'mysql') {
            // 转换到MySQL
            if (oracleToMysqlMapping[baseType]) {
                if (params) {
                    const paramList = params.split(',').map(p => p.trim());
                    if (baseType === 'NUMBER') {
                        const precision = paramList[0];
                        const scale = paramList[1];
                        return oracleToMysqlMapping[baseType](precision, scale);
                    } else {
                        return oracleToMysqlMapping[baseType](paramList[0]);
                    }
                } else {
                    return oracleToMysqlMapping[baseType]();
                }
            } else {
                return 'VARCHAR(255)'; // 默认类型
            }
        } else {
            // 转换到Doris（保持原有逻辑）
            if (oracleTypeMapping[baseType]) {
                if (params) {
                    const paramList = params.split(',').map(p => p.trim());
                    if (baseType === 'NUMBER') {
                        const precision = paramList[0];
                        const scale = paramList[1];
                        if (precision === '*') {
                            return 'BIGINT';
                        } else {
                            return oracleTypeMapping[baseType](precision, scale);
                        }
                    } else {
                        return oracleTypeMapping[baseType](paramList[0]);
                    }
                } else {
                    return oracleTypeMapping[baseType]();
                }
            } else {
                return 'VARCHAR(65533)'; // 默认类型
            }
        }
    }

    // 解析Oracle DDL
    function parseOracleDDL(ddl, targetDb = 'doris') {
        const result = {
            tableName: '',
            columns: [],
            constraints: [],
            tableComment: null
        };

        // 清理DDL，移除注释和多余空格
        let cleanDDL = ddl.replace(/--.*$/gm, '') // 移除注释
            .replace(/\s+/g, ' ')    // 合并多个空格
            .trim();

        // 提取表名 - 支持带引号的schema.table格式
        const tableMatch = cleanDDL.match(/CREATE\s+TABLE\s+(?:"?\w+"?\.)?"?(\w+)"?\s*\(/i);
        if (!tableMatch) {
            throw new Error('无法识别CREATE TABLE语句');
        }
        result.tableName = tableMatch[1].toLowerCase();

        // 提取列定义部分
        const startIndex = cleanDDL.indexOf('(');
        const endIndex = cleanDDL.lastIndexOf(')');
        if (startIndex === -1 || endIndex === -1) {
            throw new Error('无法找到表定义的括号');
        }

        const columnsSection = cleanDDL.substring(startIndex + 1, endIndex);

        // 分割列定义，考虑括号内的逗号
        const columnDefs = splitColumnDefinitions(columnsSection);

        for (let columnDef of columnDefs) {
            const column = parseColumnDefinition(columnDef.trim(), targetDb);
            if (column) {
                result.columns.push(column);
            }
        }

        // 提取表级COMMENT - 查找表定义后的COMMENT语句
        const tableCommentMatch = ddl.match(/COMMENT\s+ON\s+TABLE\s+(?:"?\w+"?\.)?"?\w+"?\s+IS\s+['"]([^'"]*)['"]/i);
        if (tableCommentMatch) {
            result.tableComment = tableCommentMatch[1];
        }

        // 提取列级COMMENT - Oracle的COMMENT ON COLUMN语句
        const columnCommentMatches = ddl.matchAll(/COMMENT\s+ON\s+COLUMN\s+(?:"?\w+"?\.)?"?(\w+)"?\."?(\w+)"?\s+IS\s+['"]([^'"]*)['"]/gi);
        for (const match of columnCommentMatches) {
            const columnName = match[2].toLowerCase();
            const comment = match[3];
            // 找到对应的列并添加注释
            const column = result.columns.find(col => col.name === columnName);
            if (column) {
                column.comment = comment;
            }
        }

        return result;
    }

    // 解析MySQL DDL
    function parseMySQLDDL(ddl) {
        const result = {
            tableName: '',
            columns: [],
            constraints: [],
            tableComment: null
        };

        // 清理DDL，移除注释和多余空格
        let cleanDDL = ddl.replace(/--.*$/gm, '') // 移除单行注释
            .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
            .replace(/\s+/g, ' ')    // 合并多个空格
            .trim();

        // 提取表名 - 支持带反引号的schema.table格式
        const tableMatch = cleanDDL.match(/CREATE\s+TABLE\s+(?:`?\w+`?\.)?`?(\w+)`?\s*\(/i);
        if (!tableMatch) {
            throw new Error('无法识别CREATE TABLE语句');
        }
        result.tableName = tableMatch[1].toLowerCase();

        // 提取列定义部分
        const startIndex = cleanDDL.indexOf('(');
        const endIndex = cleanDDL.lastIndexOf(')');
        if (startIndex === -1 || endIndex === -1) {
            throw new Error('无法找到表定义的括号');
        }

        const columnsSection = cleanDDL.substring(startIndex + 1, endIndex);

        // 分割列定义，考虑括号内的逗号
        const columnDefs = splitColumnDefinitions(columnsSection);

        for (let columnDef of columnDefs) {
            const column = parseMySQLColumnDefinition(columnDef.trim());
            if (column) {
                result.columns.push(column);
            }
        }

        // 提取表级COMMENT - MySQL表定义末尾的COMMENT
        const tableCommentMatch = ddl.match(/\)\s*[^)]*COMMENT\s*=\s*['"]([^'"]*)['"]/i);
        if (tableCommentMatch) {
            result.tableComment = tableCommentMatch[1];
        }

        return result;
    }

    // 智能分割列定义，考虑括号内的逗号
    function splitColumnDefinitions(text) {
        const result = [];
        let current = '';
        let parenthesesCount = 0;

        for (let i = 0; i < text.length; i++) {
            const char = text[i];

            if (char === '(') {
                parenthesesCount++;
            } else if (char === ')') {
                parenthesesCount--;
            } else if (char === ',' && parenthesesCount === 0) {
                if (current.trim()) {
                    result.push(current.trim());
                }
                current = '';
                continue;
            }

            current += char;
        }

        if (current.trim()) {
            result.push(current.trim());
        }

        return result;
    }

    // 解析列定义
    function parseColumnDefinition(definition, targetDb = 'doris') {
        definition = definition.trim();

        // 如果定义为空，直接返回null
        if (!definition) {
            return null;
        }

        // 更精确地识别约束定义，避免误判列名
        const upperDef = definition.toUpperCase();

        // 检查是否是真正的约束定义（不是列名）
        if (upperDef.startsWith('CONSTRAINT ') ||
            upperDef.startsWith('PRIMARY KEY') ||
            upperDef.startsWith('FOREIGN KEY') ||
            (upperDef.startsWith('UNIQUE') && (upperDef.includes('(') || upperDef.includes('KEY'))) ||
            (upperDef.startsWith('CHECK') && upperDef.includes('(') && !upperDef.match(/^CHECK_\w+\s+/))) {
            return null;
        }

        // 支持带引号的列名的正则表达式
        const columnRegex = /^"?(\w+)"?\s+([A-Z0-9_]+(?:\([^)]+\))?)\s*(.*?)$/i;
        const match = definition.match(columnRegex);

        if (!match) {
            return null;
        }

        const column = {
            name: match[1].toLowerCase(),
            type: '',
            nullable: true,
            defaultValue: null,
            comment: null
        };

        // 解析数据类型
        const typeStr = match[2].toUpperCase();
        const typeMatch = typeStr.match(/^([A-Z0-9_]+)(?:\(([^)]+)\))?$/);

        if (typeMatch) {
            const baseType = typeMatch[1];
            const params = typeMatch[2];

            // 使用目标数据库相关的类型转换
            column.type = convertOracleTypeToTarget(baseType, params, targetDb);
        }

        // 解析其他属性，移除ENABLE关键字
        let attributes = match[3].toUpperCase();
        attributes = attributes.replace(/\s+ENABLE\s*/g, ' ').trim();

        // 检查NOT NULL
        if (attributes.includes('NOT NULL')) {
            column.nullable = false;
        }

        // 检查DEFAULT值
        const defaultMatch = attributes.match(/DEFAULT\s+([^,\s]+(?:\s+[^,\s]+)*)/i);
        if (defaultMatch) {
            let defaultValue = defaultMatch[1].trim();
            // 处理特殊的默认值
            if (defaultValue === 'SYSDATE' || defaultValue === 'CURRENT_TIMESTAMP') {
                defaultValue = 'CURRENT_TIMESTAMP';
            } else if (defaultValue.match(/^-?\d+(\.\d+)?$/)) {
                // 数字类型的默认值不需要引号
                defaultValue = defaultValue;
            } else if (defaultValue.match(/^'.*'$/)) {
                // 已经有单引号的字符串，转换为双引号
                defaultValue = `"${defaultValue.slice(1, -1)}"`;
            } else if (!defaultValue.startsWith('"') && !defaultValue.startsWith("'")) {
                // 其他字符串值加双引号
                defaultValue = `"${defaultValue}"`;
            }
            column.defaultValue = defaultValue;
        }

        // 检查COMMENT注释 - 支持单引号和双引号
        const commentMatch = definition.match(/COMMENT\s+['"]([^'"]*)['"]/i);
        if (commentMatch) {
            column.comment = commentMatch[1];
        }

        return column;
    }

    // 解析MySQL列定义
    function parseMySQLColumnDefinition(definition) {
        definition = definition.trim();

        // 如果定义为空，直接返回null
        if (!definition) {
            return null;
        }

        // 更精确地识别约束定义，避免误判列名
        const upperDef = definition.toUpperCase();

        // 检查是否是真正的约束定义（不是列名）
        if (upperDef.startsWith('CONSTRAINT ') ||
            upperDef.startsWith('PRIMARY KEY') ||
            upperDef.startsWith('FOREIGN KEY') ||
            (upperDef.startsWith('UNIQUE') && (upperDef.includes('(') || upperDef.includes('KEY'))) ||
            (upperDef.startsWith('CHECK') && upperDef.includes('(') && !upperDef.match(/^CHECK_\w+\s+/)) ||
            upperDef.startsWith('KEY ') ||
            upperDef.startsWith('INDEX ')) {
            return null;
        }

        // 支持带反引号的列名的正则表达式
        const columnRegex = /^`?(\w+)`?\s+([A-Z0-9_]+(?:\([^)]+\))?)\s*(.*?)$/i;
        const match = definition.match(columnRegex);

        if (!match) {
            return null;
        }

        const column = {
            name: match[1].toLowerCase(),
            type: '',
            nullable: true,
            defaultValue: null,
            comment: null
        };

        // 解析数据类型
        const typeStr = match[2].toUpperCase();
        const typeMatch = typeStr.match(/^([A-Z0-9_]+)(?:\(([^)]+)\))?$/);

        if (typeMatch) {
            const baseType = typeMatch[1];
            const params = typeMatch[2];
            const typeMapping = getCurrentTypeMapping();

            if (typeMapping[baseType]) {
                if (params) {
                    const paramList = params.split(',').map(p => p.trim());
                    if (baseType === 'DECIMAL' || baseType === 'NUMERIC') {
                        const precision = paramList[0];
                        const scale = paramList[1];
                        column.type = typeMapping[baseType](precision, scale);
                    } else if (['TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'INTEGER', 'BIGINT'].includes(baseType)) {
                        // 检查是否有UNSIGNED属性
                        const attributes = match[3].toUpperCase();
                        const isUnsigned = attributes.includes('UNSIGNED');
                        column.type = typeMapping[baseType](paramList[0], isUnsigned);
                    } else {
                        column.type = typeMapping[baseType](paramList[0]);
                    }
                } else {
                    // 检查整数类型的UNSIGNED属性
                    if (['TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'INTEGER', 'BIGINT'].includes(baseType)) {
                        const attributes = match[3].toUpperCase();
                        const isUnsigned = attributes.includes('UNSIGNED');
                        column.type = typeMapping[baseType](null, isUnsigned);
                    } else {
                        column.type = typeMapping[baseType]();
                    }
                }
            } else {
                // 未知类型默认为VARCHAR
                column.type = 'VARCHAR(65533)';
            }
        }

        // 解析其他属性
        let attributes = match[3].toUpperCase();

        // 检查NOT NULL
        if (attributes.includes('NOT NULL')) {
            column.nullable = false;
        }

        // 检查DEFAULT值
        const defaultMatch = attributes.match(/DEFAULT\s+([^,\s]+(?:\s+[^,\s]+)*)/i);
        if (defaultMatch) {
            let defaultValue = defaultMatch[1].trim();
            // 处理特殊的默认值
            if (defaultValue === 'CURRENT_TIMESTAMP' || defaultValue === 'NOW()') {
                defaultValue = 'CURRENT_TIMESTAMP';
            } else if (defaultValue.match(/^-?\d+(\.\d+)?$/)) {
                // 数字类型的默认值不需要引号
                defaultValue = defaultValue;
            } else if (defaultValue.match(/^'.*'$/)) {
                // 已经有单引号的字符串，转换为双引号
                defaultValue = `"${defaultValue.slice(1, -1)}"`;
            } else if (!defaultValue.startsWith('"') && !defaultValue.startsWith("'")) {
                // 其他字符串值加双引号
                defaultValue = `"${defaultValue}"`;
            }
            column.defaultValue = defaultValue;
        }

        // 检查COMMENT注释 - 支持单引号和双引号
        const commentMatch = definition.match(/COMMENT\s+['"]([^'"]*)['"]/i);
        if (commentMatch) {
            column.comment = commentMatch[1];
        }

        return column;
    }

    // 生成Doris DDL
    function generateDorisDDL(parsedData, tablePrefix = '', includeNotNull = true, versionType = 'rid') {
        if (!parsedData.tableName || parsedData.columns.length === 0) {
            return '-- 请输入有效的建表语句';
        }

        const tableName = tablePrefix + parsedData.tableName;
        let ddl = `CREATE TABLE ${tableName} (\n`;

        // 根据版本类型定义固定列
        const fixedColumns = versionType === 'rid' ? {
            primary: { name: 'rid', type: 'BIGINT NOT NULL' },
            timestamp: { name: 'sys_time_stamp_doris', type: 'DATETIME(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)' },
            deleteFlag: { name: 'if_delete_doris', type: 'CHAR NOT NULL DEFAULT 0' }
        } : {
            primary: { name: 'id', type: 'VARCHAR(32) NOT NULL' },
            timestamp: { name: 'sys_time_stamp_doris', type: 'DATETIME(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)' },
            deleteFlag: { name: 'del_flag', type: 'CHAR NOT NULL DEFAULT 0' }
        };

        // 过滤掉与固定列同名的原有列
        const fixedColumnNames = [fixedColumns.primary.name, fixedColumns.timestamp.name, fixedColumns.deleteFlag.name];
        const filteredColumns = parsedData.columns.filter(col =>
            !fixedColumnNames.includes(col.name.toLowerCase())
        );

        // 添加主键列
        ddl += `    \`${fixedColumns.primary.name}\` ${fixedColumns.primary.type},\n`;

        // 添加过滤后的原有列
        filteredColumns.forEach(column => {
            ddl += `    \`${column.name}\` ${column.type}`;

            // 根据选项决定是否包含NOT NULL
            if (includeNotNull && !column.nullable) {
                ddl += ' NOT NULL';
            }

            // 添加列注释
            if (column.comment) {
                ddl += ` COMMENT "${column.comment}"`;
            }

            ddl += ',\n';
        });

        // 添加系统列 - 系统列不受选项影响，保持固定格式
        ddl += `    \`${fixedColumns.timestamp.name}\` ${fixedColumns.timestamp.type},\n`;
        ddl += `    \`${fixedColumns.deleteFlag.name}\` ${fixedColumns.deleteFlag.type}\n`;
        ddl += `) ENGINE=OLAP\n`;
        ddl += `UNIQUE KEY(\`${fixedColumns.primary.name}\`)\n`;

        // 使用原表的注释，如果没有则使用默认值
        const tableComment = parsedData.tableComment || "OLAP";
        ddl += `COMMENT "${tableComment}"\n`;
        ddl += `DISTRIBUTED BY HASH(\`${fixedColumns.primary.name}\`) BUCKETS AUTO\n`;
        ddl += `PROPERTIES (\n`;
        ddl += `    "replication_num" = "1",\n`;
        ddl += `    "in_memory" = "false",\n`;
        ddl += `    "storage_format" = "V2"\n`;
        ddl += `);`;

        return ddl;
    }

    // 驼峰转下划线
    function convertToSnakeCase(str) {
        return str.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '');
    }

    // 生成MySQL DDL
    function generateMySQLDDL(parsedData, tablePrefix = '') {
        if (!parsedData.tableName || parsedData.columns.length === 0) {
            return '-- 请输入有效的建表语句';
        }

        const tableName = convertToSnakeCase(tablePrefix + parsedData.tableName);

        // MySQL固定列定义
        const fixedColumns = {
            id: "id VARCHAR(32) NOT NULL COMMENT 'uuid'",
            revision: "revision INT NOT NULL DEFAULT 1 COMMENT '修订次数:自动加1'",
            del_flag: "del_flag CHAR(1) NOT NULL DEFAULT '0' COMMENT '标删标记:0：否，1：是'",
            create_time: "create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'",
            create_by: "create_by VARCHAR(64) NOT NULL COMMENT '创建人'",
            update_time: "update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'",
            update_by: "update_by VARCHAR(64) NOT NULL COMMENT '修改人'"
        };

        // 过滤固定列，转换自定义列
        const fixedColumnNames = ['id', 'revision', 'del_flag', 'create_time', 'create_by', 'update_time', 'update_by'];
        const customColumns = parsedData.columns
            .filter(col => !fixedColumnNames.includes(col.name.toLowerCase()))
            .map(col => ({
                ...col,
                name: convertToSnakeCase(col.name)
            }));

        // 生成DDL
        let ddl = `CREATE TABLE ${tableName} (\n`;
        ddl += `    ${fixedColumns.id},\n`;

        // 添加自定义列（放在id和revision之间）
        customColumns.forEach(column => {
            ddl += `    ${generateMySQLColumnDefinition(column)},\n`;
        });

        ddl += `    ${fixedColumns.revision},\n`;
        ddl += `    ${fixedColumns.del_flag},\n`;
        ddl += `    ${fixedColumns.create_time},\n`;
        ddl += `    ${fixedColumns.create_by},\n`;
        ddl += `    ${fixedColumns.update_time},\n`;
        ddl += `    ${fixedColumns.update_by},\n`;
        ddl += `    PRIMARY KEY (id)\n`;
        ddl += `) COMMENT='${parsedData.tableComment || '表描述'}';`;

        return ddl;
    }

    // 生成MySQL列定义
    function generateMySQLColumnDefinition(column) {
        let def = `${column.name} ${column.type}`;

        if (!column.nullable) {
            def += ' NOT NULL';
        }

        if (column.defaultValue) {
            // 处理默认值
            let defaultValue = column.defaultValue;
            if (defaultValue === 'SYSDATE' || defaultValue === 'CURRENT_TIMESTAMP') {
                defaultValue = 'CURRENT_TIMESTAMP';
            }
            def += ` DEFAULT ${defaultValue}`;
        }

        if (column.comment) {
            def += ` COMMENT '${column.comment}'`;
        }

        return def;
    }

    // 通用转换函数
    function convertDDL() {
        const sourceInput = document.getElementById('sourceInput').value;
        const sourceDatabase = document.getElementById('sourceDatabase').value;
        const targetDatabase = document.getElementById('targetDatabase') ? document.getElementById('targetDatabase').value : 'doris';
        const tablePrefix = document.getElementById('tablePrefix').value || '';
        const includeNotNull = false; // 移除NOT NULL选择，默认为false
        const versionType = document.getElementById('versionType').value;
        const outputElement = document.getElementById('dorisOutput');

        if (!sourceInput.trim()) {
            const sourceDbName = sourceDatabase === 'mysql' ? 'MySQL' : 'Oracle';
            const targetDbName = targetDatabase === 'mysql' ? 'MySQL' : 'Doris';
            outputElement.value = `-- 请输入${sourceDbName}建表语句`;
            return;
        }

        try {
            let parsed;
            if (sourceDatabase === 'mysql') {
                parsed = parseMySQLDDL(sourceInput);
            } else {
                parsed = parseOracleDDL(sourceInput, targetDatabase);
            }

            let targetDDL;
            if (targetDatabase === 'mysql') {
                targetDDL = generateMySQLDDL(parsed, tablePrefix);
            } else {
                targetDDL = generateDorisDDL(parsed, tablePrefix, includeNotNull, versionType);
            }

            outputElement.value = targetDDL;
        } catch (error) {
            outputElement.value = `-- 转换出错: ${error.message}`;
        }
    }

    // 保持向后兼容
    function convertToDoris() {
        convertDDL();
    }

    // 显示Toast通知
    function showToast(message) {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        toastMessage.textContent = message;

        // 显示toast
        toast.style.right = '16px';
        toast.classList.remove('opacity-0', 'invisible');
        toast.classList.add('opacity-100', 'visible');

        // 3秒后隐藏
        setTimeout(() => {
            toast.style.right = '-300px';
            toast.classList.remove('opacity-100', 'visible');
            toast.classList.add('opacity-0', 'invisible');
        }, 3000);
    }

    // 更新版本规则显示
    function updateVersionRules() {
        const versionType = document.getElementById('versionType').value;
        const primaryKeyRule = document.getElementById('primaryKeyRule');
        const systemColumnsRule = document.getElementById('systemColumnsRule');

        if (versionType === 'rid') {
            primaryKeyRule.textContent = '添加主键列 rid (BIGINT NOT NULL)';
            systemColumnsRule.textContent = '添加系统列：sys_time_stamp_doris (DATETIME(6)), if_delete_doris (CHAR)';
        } else {
            primaryKeyRule.textContent = '添加主键列 id (VARCHAR(32) NOT NULL)';
            systemColumnsRule.textContent = '添加系统列：sys_time_stamp_doris (DATETIME(6)), del_flag (CHAR)';
        }
    }

    // 数据库类型切换函数
    function updateDatabaseType() {
        const sourceDb = document.getElementById('sourceDatabase').value;
        const inputHeader = document.getElementById('inputHeader');
        const inputIcon = document.getElementById('inputIcon');
        const inputTitle = document.getElementById('inputTitle');
        const sourceInput = document.getElementById('sourceInput');
        const sourceDbHeader = document.getElementById('sourceDbHeader');

        updateTypeMappingTable();

        if (sourceDb === 'mysql') {
            inputHeader.className = 'bg-gradient-to-r from-blue-500 to-indigo-500 px-6 py-4';
            inputIcon.className = 'fas fa-database text-white text-lg';
            inputTitle.textContent = 'MySQL 建表语句';
            sourceInput.placeholder = '请输入MySQL建表语句...';
            sourceDbHeader.textContent = 'MySQL';
        } else {
            inputHeader.className = 'bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4';
            inputIcon.className = 'fab fa-oracle text-white text-lg';
            inputTitle.textContent = 'Oracle 建表语句';
            sourceInput.placeholder = '请输入Oracle建表语句...';
            sourceDbHeader.textContent = 'Oracle';
        }

        // 重新转换
        convertDDL();
    }

    // 目标数据库切换函数
    function updateTargetDatabase() {
        const targetDb = document.getElementById('targetDatabase').value;
        const outputHeader = document.getElementById('dorisHeader');
        const outputIcon = document.getElementById('outputIcon');
        const outputTitle = document.getElementById('outputTitle');
        const versionToggle = document.getElementById('versionToggle');
        const targetDbToggle = document.getElementById('targetDbToggle');

        updateTypeMappingTable();

        if (targetDb === 'mysql') {
            // MySQL模式：隐藏版本切换，更新标题和样式
            versionToggle.style.display = 'none';
            outputHeader.className = 'bg-gradient-to-r from-blue-500 to-indigo-500 px-6 py-4 transition-all duration-300';
            outputIcon.className = 'fas fa-database text-white text-lg';
            outputTitle.textContent = 'MySQL 建表语句';
            targetDbToggle.style.backgroundColor = 'rgba(59, 130, 246, 0.8)'; // 蓝色
            document.getElementById('targetDbText').textContent = 'MySQL';
        } else {
            // Doris模式：显示版本切换
            versionToggle.style.display = 'block';
            outputHeader.className = 'bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 transition-all duration-300';
            outputIcon.className = 'fas fa-database text-white text-lg';
            outputTitle.textContent = 'Doris 建表语句';
            targetDbToggle.style.backgroundColor = 'rgba(34, 197, 94, 0.8)'; // 绿色
            document.getElementById('targetDbText').textContent = 'Doris';
        }

        // 重新转换
        convertDDL();
    }

    // 更新类型映射表
    function updateTypeMappingTable() {
        const sourceDb = document.getElementById('sourceDatabase').value;
        const targetDb = document.getElementById('targetDatabase') ? document.getElementById('targetDatabase').value : 'doris';
        const sourceDbHeader = document.getElementById('sourceDbHeader');
        const typeMappingBody = document.getElementById('typeMappingBody');

        // 更新表头
        sourceDbHeader.textContent = sourceDb === 'mysql' ? 'MySQL' : 'Oracle';

        // 更新映射内容
        if (sourceDb === 'oracle' && targetDb === 'mysql') {
            // Oracle到MySQL的映射
            typeMappingBody.innerHTML = `
                <tr><td class="py-1">NUMBER</td><td class="py-1">INT/BIGINT/DECIMAL</td></tr>
                <tr><td class="py-1">VARCHAR2/NVARCHAR2</td><td class="py-1">VARCHAR(原长度*3)</td></tr>
                <tr><td class="py-1">CHAR/NCHAR</td><td class="py-1">CHAR(原长度*3)</td></tr>
                <tr><td class="py-1">DATE/TIMESTAMP</td><td class="py-1">DATETIME</td></tr>
                <tr><td class="py-1">CLOB/NCLOB</td><td class="py-1">LONGTEXT</td></tr>
                <tr><td class="py-1">BLOB</td><td class="py-1">LONGBLOB</td></tr>
                <tr><td class="py-1">INTEGER</td><td class="py-1">INT</td></tr>
                <tr><td class="py-1">FLOAT</td><td class="py-1">FLOAT</td></tr>
                <tr><td class="py-1">BINARY_DOUBLE</td><td class="py-1">DOUBLE</td></tr>
                <tr><td class="py-1">ROWID</td><td class="py-1">VARCHAR(18)</td></tr>
            `;
        } else if (sourceDb === 'mysql' && targetDb === 'doris') {
            // MySQL到Doris的映射
            typeMappingBody.innerHTML = `
                <tr><td class="py-1">INT/INTEGER</td><td class="py-1">INT/BIGINT</td></tr>
                <tr><td class="py-1">BIGINT</td><td class="py-1">BIGINT</td></tr>
                <tr><td class="py-1">DECIMAL/NUMERIC</td><td class="py-1">DECIMAL</td></tr>
                <tr><td class="py-1">VARCHAR</td><td class="py-1">VARCHAR(65533)</td></tr>
                <tr><td class="py-1">CHAR</td><td class="py-1">CHAR(255)</td></tr>
                <tr><td class="py-1">TEXT</td><td class="py-1">STRING</td></tr>
                <tr><td class="py-1">DATETIME/TIMESTAMP</td><td class="py-1">DATETIME</td></tr>
                <tr><td class="py-1">DATE</td><td class="py-1">DATE</td></tr>
                <tr><td class="py-1">FLOAT/DOUBLE</td><td class="py-1">FLOAT/DOUBLE</td></tr>
                <tr><td class="py-1">BOOLEAN</td><td class="py-1">BOOLEAN</td></tr>
            `;
        } else {
            // Oracle到Doris的映射（默认）
            typeMappingBody.innerHTML = `
                <tr><td class="py-1">NUMBER</td><td class="py-1">DECIMAL/BIGINT</td></tr>
                <tr><td class="py-1">INTEGER</td><td class="py-1">BIGINT</td></tr>
                <tr><td class="py-1">VARCHAR2/NVARCHAR2</td><td class="py-1">VARCHAR(65533)</td></tr>
                <tr><td class="py-1">CHAR/NCHAR</td><td class="py-1">CHAR(255)</td></tr>
                <tr><td class="py-1">DATE/TIMESTAMP</td><td class="py-1">DATETIME</td></tr>
                <tr><td class="py-1">CLOB/NCLOB</td><td class="py-1">STRING</td></tr>
                <tr><td class="py-1">BLOB/RAW</td><td class="py-1">BINARY</td></tr>
                <tr><td class="py-1">BINARY_FLOAT</td><td class="py-1">FLOAT</td></tr>
                <tr><td class="py-1">BINARY_DOUBLE</td><td class="py-1">DOUBLE</td></tr>
                <tr><td class="py-1">ROWID</td><td class="py-1">VARCHAR(65533)</td></tr>
            `;
        }
    }

    // 事件监听器
    document.getElementById('sourceDatabase').addEventListener('change', updateDatabaseType);
    if (document.getElementById('targetDatabase')) {
        document.getElementById('targetDatabase').addEventListener('change', updateTargetDatabase);
    }
    document.getElementById('versionType').addEventListener('change', () => {
        updateVersionRules();
        convertDDL();
    });
    document.getElementById('sourceInput').addEventListener('input', convertDDL);
    document.getElementById('tablePrefix').addEventListener('input', convertDDL);
    // includeNotNull事件监听器已移除

    // 初始化页面
    updateVersionRules();
    updateTypeMappingTable();

    // 初始化按钮和header颜色
    function initializeButtonColors() {
        const sourceDb = document.getElementById('sourceDatabase').value;
        const targetDb = document.getElementById('targetDatabase') ? document.getElementById('targetDatabase').value : 'doris';
        const versionType = document.getElementById('versionType').value;
        const sourceDbToggle = document.getElementById('sourceDbToggle');
        const versionToggle = document.getElementById('versionToggle');
        const targetDbToggle = document.getElementById('targetDbToggle');
        const dorisHeader = document.getElementById('dorisHeader');

        // 设置源数据库切换按钮颜色
        if (sourceDb === 'oracle') {
            sourceDbToggle.style.backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 橙色 for Oracle
        } else {
            sourceDbToggle.style.backgroundColor = 'rgba(59, 130, 246, 0.8)'; // 蓝色 for MySQL
        }

        // 设置目标数据库切换按钮颜色
        if (targetDb === 'mysql') {
            targetDbToggle.style.backgroundColor = 'rgba(59, 130, 246, 0.8)'; // 蓝色 for MySQL
            versionToggle.style.display = 'none'; // 隐藏版本切换
            dorisHeader.className = 'bg-gradient-to-r from-blue-500 to-indigo-500 px-6 py-4 transition-all duration-300';
        } else {
            targetDbToggle.style.backgroundColor = 'rgba(34, 197, 94, 0.8)'; // 绿色 for Doris
            versionToggle.style.display = 'block'; // 显示版本切换

            // 设置版本切换按钮和Doris header颜色
            if (versionType === 'rid') {
                versionToggle.style.backgroundColor = 'rgba(34, 197, 94, 0.8)'; // 绿色 for RID版本
                dorisHeader.className = 'bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 transition-all duration-300';
            } else {
                versionToggle.style.backgroundColor = 'rgba(168, 85, 247, 0.8)'; // 紫色 for ID版本
                dorisHeader.className = 'bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-4 transition-all duration-300';
            }
        }
    }

    // 调用初始化函数
    initializeButtonColors();

    // 复制功能
    document.getElementById('copyOutput').addEventListener('click', async () => {
        const output = document.getElementById('dorisOutput').value;
        if (output && !output.startsWith('--')) {
            try {
                await navigator.clipboard.writeText(output);
                showToast('已复制到剪贴板');
            } catch (err) {
                // 降级方案
                const textarea = document.getElementById('dorisOutput');
                textarea.select();
                document.execCommand('copy');
                showToast('已复制到剪贴板');
            }
        }
    });

    // 清空输入
    document.getElementById('clearInput').addEventListener('click', () => {
        document.getElementById('sourceInput').value = '';
        document.getElementById('dorisOutput').value = '-- 转换后的Doris建表语句将在这里显示...';
    });

    // 数据库切换按钮
    document.getElementById('sourceDbToggle').addEventListener('click', () => {
        const sourceDb = document.getElementById('sourceDatabase');
        const sourceDbToggle = document.getElementById('sourceDbToggle');
        const currentValue = sourceDb.value;
        const newValue = currentValue === 'oracle' ? 'mysql' : 'oracle';
        sourceDb.value = newValue;

        // 更新按钮文本和颜色
        document.getElementById('sourceDbText').textContent = newValue === 'oracle' ? 'Oracle' : 'MySQL';

        // 切换颜色效果
        if (newValue === 'oracle') {
            sourceDbToggle.style.backgroundColor = 'rgba(255, 165, 0, 0.8)'; // 橙色 for Oracle
        } else {
            sourceDbToggle.style.backgroundColor = 'rgba(59, 130, 246, 0.8)'; // 蓝色 for MySQL
        }

        // 触发数据库类型更新
        updateDatabaseType();
    });

    // 版本切换按钮
    document.getElementById('versionToggle').addEventListener('click', () => {
        const versionType = document.getElementById('versionType');
        const versionToggle = document.getElementById('versionToggle');
        const dorisHeader = document.getElementById('dorisHeader');
        const currentValue = versionType.value;
        const newValue = currentValue === 'rid' ? 'id' : 'rid';
        versionType.value = newValue;

        // 更新按钮文本和颜色
        document.getElementById('versionText').textContent = newValue === 'rid' ? 'RID版本' : 'ID版本';

        // 切换按钮和header颜色效果
        if (newValue === 'rid') {
            versionToggle.style.backgroundColor = 'rgba(34, 197, 94, 0.8)'; // 绿色 for RID版本
            dorisHeader.className = 'bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 transition-all duration-300';
        } else {
            versionToggle.style.backgroundColor = 'rgba(168, 85, 247, 0.8)'; // 紫色 for ID版本
            dorisHeader.className = 'bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-4 transition-all duration-300';
        }

        // 触发版本规则更新
        updateVersionRules();
        convertDDL();
    });

    // 目标数据库切换按钮
    document.getElementById('targetDbToggle').addEventListener('click', () => {
        const targetDb = document.getElementById('targetDatabase');
        const currentValue = targetDb.value;
        const newValue = currentValue === 'doris' ? 'mysql' : 'doris';
        targetDb.value = newValue;

        // 触发目标数据库更新
        updateTargetDatabase();
    });

    // 移除示例按钮功能（保留注释以备将来使用）
    /*
    document.getElementById('loadExample').addEventListener('click', () => {
        // 示例代码已移除
    });
    */


    // Tooltip控制函数
    function showRulesTooltip() {
        const tooltip = document.getElementById('rulesTooltip');
        tooltip.classList.remove('opacity-0', 'invisible');
        tooltip.classList.add('opacity-100', 'visible');
    }

    function hideRulesTooltip() {
        const tooltip = document.getElementById('rulesTooltip');
        tooltip.classList.remove('opacity-100', 'visible');
        tooltip.classList.add('opacity-0', 'invisible');
    }

    // 防止tooltip在鼠标移入时消失
    document.getElementById('rulesTooltip').addEventListener('mouseenter', function() {
        showRulesTooltip();
    });

    document.getElementById('rulesTooltip').addEventListener('mouseleave', function() {
        hideRulesTooltip();
    });
</script>
</body>
</html>
