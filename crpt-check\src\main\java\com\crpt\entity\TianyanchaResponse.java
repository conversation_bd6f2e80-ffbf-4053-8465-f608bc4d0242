package com.crpt.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;

/**
 * 天眼查API响应实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TianyanchaResponse {

    /**
     * 错误码
     */
    private String error_code;

    /**
     * 原因
     */
    private String reason;

    /**
     * 结果数据
     */
    private ResultData result;

    public String getError_code() {
        return error_code;
    }

    public void setError_code(String error_code) {
        this.error_code = error_code;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public ResultData getResult() {
        return result;
    }

    public void setResult(ResultData result) {
        this.result = result;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultData {
        /**
         * 总数
         */
        private Integer total;

        /**
         * 企业信息列表
         */
        private List<CompanyInfo> items;

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        public List<CompanyInfo> getItems() {
            return items;
        }

        public void setItems(List<CompanyInfo> items) {
            this.items = items;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CompanyInfo {
        /**
         * 企业名称
         */
        private String name;

        /**
         * 社会信用代码
         */
        private String creditCode;

        /**
         * 统一社会信用代码
         */
        private String regNumber;

        /**
         * 企业ID
         */
        private Long id;

        /**
         * 企业类型
         */
        private String type;

        /**
         * 企业状态
         */
        private String regStatus;

        /**
         * 企业类型（详细）
         */
        private String companyType;

        /**
         * 注册地址
         */
        private String base;

        /**
         * 法定代表人
         */
        private String legalPersonName;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCreditCode() {
            return creditCode;
        }

        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        public String getRegNumber() {
            return regNumber;
        }

        public void setRegNumber(String regNumber) {
            this.regNumber = regNumber;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getRegStatus() {
            return regStatus;
        }

        public void setRegStatus(String regStatus) {
            this.regStatus = regStatus;
        }

        public String getCompanyType() {
            return companyType;
        }

        public void setCompanyType(String companyType) {
            this.companyType = companyType;
        }

        public String getBase() {
            return base;
        }

        public void setBase(String base) {
            this.base = base;
        }

        public String getLegalPersonName() {
            return legalPersonName;
        }

        public void setLegalPersonName(String legalPersonName) {
            this.legalPersonName = legalPersonName;
        }
    }
}
