{"groups": [{"name": "ai.analysis", "type": "com.crpt.config.AiAnalysisConfig", "sourceType": "com.crpt.config.AiAnalysisConfig"}], "properties": [{"name": "ai.analysis.api-key", "type": "java.lang.String", "description": "AI API密钥", "sourceType": "com.crpt.config.AiAnalysisConfig"}, {"name": "ai.analysis.api-url", "type": "java.lang.String", "description": "AI API地址（默认使用DeepSeek API）", "sourceType": "com.crpt.config.AiAnalysisConfig"}, {"name": "ai.analysis.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用AI分析", "sourceType": "com.crpt.config.AiAnalysisConfig"}, {"name": "ai.analysis.model", "type": "java.lang.String", "description": "AI模型名称（默认使用DeepSeek Chat模型）", "sourceType": "com.crpt.config.AiAnalysisConfig"}, {"name": "ai.analysis.timeout", "type": "java.lang.Integer", "description": "请求超时时间（秒）", "sourceType": "com.crpt.config.AiAnalysisConfig"}], "hints": []}