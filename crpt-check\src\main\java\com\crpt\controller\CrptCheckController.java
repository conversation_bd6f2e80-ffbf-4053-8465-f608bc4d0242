package com.crpt.controller;

import com.crpt.entity.ApiResponse;
import com.crpt.service.CrptCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 企业信息匹配检查控制器
 */
@RestController
@RequestMapping("/api/crpt")
@CrossOrigin(origins = "*")
public class CrptCheckController {

    private static final Logger log = LoggerFactory.getLogger(CrptCheckController.class);
    
    @Autowired
    private CrptCheckService crptCheckService;
    
    /**
     * 执行企业信息匹配检查
     */
    @PostMapping("/check")
    public ApiResponse<String> check(
            @RequestParam String db,
            @RequestParam String tableName,
            @RequestParam String sql,
            @RequestParam(required = false) String creditCodeColumn,
            @RequestParam(required = false) String companyNameColumn,
            @RequestParam(required = false) String businessKeyColumn,
            @RequestParam(required = false) String delMarkColumn,
            @RequestParam(required = false) String subOrgColumn,
            @RequestParam(required = false) String updateSql,
            @RequestParam(required = false, defaultValue = "false") Boolean useAi) {
        
        try {
            // 参数校验
            if (!StringUtils.hasText(db)) {
                return ApiResponse.error(400, "数据库类型不能为空");
            }
            if (!StringUtils.hasText(tableName)) {
                return ApiResponse.error(400, "表名不能为空");
            }
            if (!StringUtils.hasText(sql)) {
                return ApiResponse.error(400, "SQL语句不能为空");
            }
            
            // 数据库类型校验
            if (!"mysql".equalsIgnoreCase(db) && !"oracle".equalsIgnoreCase(db)) {
                return ApiResponse.error(400, "不支持的数据库类型，仅支持mysql和oracle");
            }



            log.info("开始执行企业信息匹配检查 - 数据库类型: {}, 表名: {}, 使用AI: {}", db, tableName, useAi);

            // 异步执行检查任务
            new Thread(() -> {
                try {
                    crptCheckService.executeCheck(db, tableName, sql, creditCodeColumn, companyNameColumn,
                            businessKeyColumn, delMarkColumn, subOrgColumn, updateSql, useAi);
                } catch (Exception e) {
                    log.error("企业信息匹配检查执行失败", e);
                }
            }).start();
            
            return ApiResponse.success("企业信息匹配检查任务已启动，请查看日志文件获取处理结果");
            
        } catch (Exception e) {
            log.error("企业信息匹配检查失败", e);
            return ApiResponse.error("企业信息匹配检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行更新SQL语句
     */
    @PostMapping("/executeUpdate")
    public ApiResponse<String> executeUpdate(
            @RequestParam String dbType,
            @RequestParam String updateSql) {

        try {
            // 参数校验
            if (!StringUtils.hasText(dbType)) {
                return ApiResponse.error(400, "数据库类型不能为空");
            }
            if (!StringUtils.hasText(updateSql)) {
                return ApiResponse.error(400, "更新SQL不能为空");
            }

            // 数据库类型校验
            if (!"mysql".equalsIgnoreCase(dbType) && !"oracle".equalsIgnoreCase(dbType)) {
                return ApiResponse.error(400, "不支持的数据库类型，仅支持mysql和oracle");
            }

            log.info("执行更新SQL - 数据库类型: {}, SQL: {}", dbType, updateSql);

            // 执行更新SQL
            int affectedRows = crptCheckService.executeUpdateSql(dbType, updateSql);

            String message = String.format("更新SQL执行成功，影响行数: %d", affectedRows);
            log.info(message);

            return ApiResponse.success(message);

        } catch (Exception e) {
            log.error("执行更新SQL失败", e);
            return ApiResponse.error("执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public ApiResponse<String> status() {
        return ApiResponse.success("系统运行正常");
    }
}
