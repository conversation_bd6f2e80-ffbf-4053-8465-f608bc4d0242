package com.crpt.util;

import com.crpt.entity.MatchResult;
import com.crpt.entity.ConfigurationInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 日志文件写入工具
 */
@Component
public class LogFileWriter {

    private static final Logger log = LoggerFactory.getLogger(LogFileWriter.class);

    @Value("${crpt.check.log-file-path}")
    private String logFilePath;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 初始化新的日志文件（每次检查任务开始时调用）
     */
    public String initializeLogFile(String dbType, String tableName, LocalDateTime startTime) {
        try {
            // 确保日志目录存在
            File logDir = new File(logFilePath);
            if (!logDir.exists()) {
                logDir.mkdirs();
            }

            // 生成日志文件名：数据库类型+表名+请求开始时间
            String timeStr = startTime.format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
            String fileName = String.format("%s-%s-%s.log", dbType.toLowerCase(), tableName, timeStr);

            File logFile = new File(logDir, fileName);

            // 创建文件并写入头部信息
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(logFile, false))) {
                writer.write("# 企业信息匹配检查日志");
                writer.newLine();
                writer.write("# 数据库类型: " + dbType);
                writer.newLine();
                writer.write("# 表名: " + tableName);
                writer.newLine();
                writer.write("# 开始时间: " + startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                writer.newLine();
                writer.write("# 日志格式: JSON");
                writer.newLine();
                writer.write("# ==========================================");
                writer.newLine();
                writer.flush();
            }

            log.info("初始化日志文件: {}", logFile.getAbsolutePath());
            return fileName;

        } catch (IOException e) {
            log.error("初始化日志文件失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 写入匹配结果到指定日志文件
     */
    public void writeMatchResult(String logFileName, MatchResult matchResult) {
        if (logFileName == null || logFileName.trim().isEmpty()) {
            log.warn("日志文件名不能为空，无法写入匹配结果");
            return;
        }

        try {
            File logFile = new File(logFilePath, logFileName);

            // 写入日志
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(logFile, true))) {
                String logLine = objectMapper.writeValueAsString(matchResult);
                writer.write(logLine);
                writer.newLine();
                writer.flush();
            }

            log.debug("匹配结果已写入日志文件: {}", logFile.getAbsolutePath());

        } catch (IOException e) {
            log.error("写入匹配结果日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 完成指定任务的日志记录
     */
    public void finalizeLogFile(String logFileName, int totalProcessed, int successCount, int failureCount) {
        if (logFileName == null || logFileName.trim().isEmpty()) {
            log.warn("日志文件名不能为空，无法完成日志记录");
            return;
        }

        try {
            File logFile = new File(logFilePath, logFileName);

            // 写入统计信息
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(logFile, true))) {
                writer.write("# ==========================================");
                writer.newLine();
                writer.write("# 任务完成统计");
                writer.newLine();
                writer.write("# 总处理数量: " + totalProcessed);
                writer.newLine();
                writer.write("# 成功数量: " + successCount);
                writer.newLine();
                writer.write("# 失败数量: " + failureCount);
                writer.newLine();
                writer.write("# 完成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                writer.newLine();
                writer.flush();
            }

            log.info("日志文件记录完成: {}, 总处理: {}, 成功: {}, 失败: {}",
                    logFileName, totalProcessed, successCount, failureCount);

        } catch (IOException e) {
            log.error("完成日志文件记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 写入配置信息到指定日志文件（JSON格式）
     */
    public synchronized void writeConfigurationInfo(String logFileName, Map<String, Object> configInfo) {
        if (logFileName == null || logFileName.trim().isEmpty()) {
            log.warn("日志文件名不能为空，无法写入配置信息");
            return;
        }

        try {
            String logFilePath = this.logFilePath + File.separator + logFileName;

            // 创建配置信息对象
            ConfigurationInfo config = new ConfigurationInfo();
            config.setConfigType((String) configInfo.get("configType"));
            config.setTimestamp((String) configInfo.get("timestamp"));
            config.setDatabaseType((String) configInfo.get("databaseType"));
            config.setTableName((String) configInfo.get("tableName"));
            config.setUseAi((Boolean) configInfo.get("useAi"));
            config.setQuerySql((String) configInfo.get("querySql"));
            config.setUpdateSql((String) configInfo.get("updateSql"));
            config.setCreditCodeColumn((String) configInfo.get("creditCodeColumn"));
            config.setCompanyNameColumn((String) configInfo.get("companyNameColumn"));
            config.setBusinessKeyColumn((String) configInfo.get("businessKeyColumn"));
            config.setDelMarkColumn((String) configInfo.get("delMarkColumn"));
            config.setSubOrgColumn((String) configInfo.get("subOrgColumn"));
            config.setApiInterval((Integer) configInfo.get("apiInterval"));
            config.setPageSize((Integer) configInfo.get("pageSize"));

            // 添加解析后的字段信息
            @SuppressWarnings("unchecked")
            Map<String, Object> parsedFieldInfo = (Map<String, Object>) configInfo.get("parsedFieldInfo");
            if (parsedFieldInfo != null) {
                config.setParsedFieldInfo(parsedFieldInfo);
            }

            // 将配置信息转换为JSON并写入文件
            try (FileWriter writer = new FileWriter(logFilePath, true)) {
                String configJson = objectMapper.writeValueAsString(config);
                writer.write(configJson);
                writer.write("\n");
                writer.flush();
            }

            log.debug("配置信息已写入日志文件: {}", logFilePath);

        } catch (IOException e) {
            log.error("写入配置信息到日志文件失败", e);
        }
    }

    /**
     * 获取日志文件路径
     */
    public String getLogFilePath() {
        return logFilePath;
    }
}
