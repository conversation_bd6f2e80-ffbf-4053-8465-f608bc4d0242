package com.crpt.entity;

import java.util.Map;

/**
 * 匹配检查配置信息实体类
 */
public class ConfigurationInfo {

    /**
     * 配置类型
     */
    private String configType;

    /**
     * 记录时间
     */
    private String timestamp;

    /**
     * 数据库类型
     */
    private String databaseType;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 是否使用AI分析
     */
    private Boolean useAi;

    /**
     * 查询SQL
     */
    private String querySql;

    /**
     * 更新SQL
     */
    private String updateSql;

    /**
     * 社会信用代码列名
     */
    private String creditCodeColumn;

    /**
     * 企业名称列名
     */
    private String companyNameColumn;

    /**
     * 业务主键列名
     */
    private String businessKeyColumn;

    /**
     * 删除标记列名
     */
    private String delMarkColumn;

    /**
     * 分支机构列名
     */
    private String subOrgColumn;

    /**
     * API调用间隔（毫秒）
     */
    private Integer apiInterval;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 解析后的字段信息
     */
    private Map<String, Object> parsedFieldInfo;

    // Getters and Setters

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getDatabaseType() {
        return databaseType;
    }

    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Boolean getUseAi() {
        return useAi;
    }

    public void setUseAi(Boolean useAi) {
        this.useAi = useAi;
    }

    public String getQuerySql() {
        return querySql;
    }

    public void setQuerySql(String querySql) {
        this.querySql = querySql;
    }

    public String getUpdateSql() {
        return updateSql;
    }

    public void setUpdateSql(String updateSql) {
        this.updateSql = updateSql;
    }

    public String getCreditCodeColumn() {
        return creditCodeColumn;
    }

    public void setCreditCodeColumn(String creditCodeColumn) {
        this.creditCodeColumn = creditCodeColumn;
    }

    public String getCompanyNameColumn() {
        return companyNameColumn;
    }

    public void setCompanyNameColumn(String companyNameColumn) {
        this.companyNameColumn = companyNameColumn;
    }

    public String getBusinessKeyColumn() {
        return businessKeyColumn;
    }

    public void setBusinessKeyColumn(String businessKeyColumn) {
        this.businessKeyColumn = businessKeyColumn;
    }

    public String getDelMarkColumn() {
        return delMarkColumn;
    }

    public void setDelMarkColumn(String delMarkColumn) {
        this.delMarkColumn = delMarkColumn;
    }

    public String getSubOrgColumn() {
        return subOrgColumn;
    }

    public void setSubOrgColumn(String subOrgColumn) {
        this.subOrgColumn = subOrgColumn;
    }

    public Integer getApiInterval() {
        return apiInterval;
    }

    public void setApiInterval(Integer apiInterval) {
        this.apiInterval = apiInterval;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Map<String, Object> getParsedFieldInfo() {
        return parsedFieldInfo;
    }

    public void setParsedFieldInfo(Map<String, Object> parsedFieldInfo) {
        this.parsedFieldInfo = parsedFieldInfo;
    }
}
