<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天眼查企业信息匹配系统</title>
    <script src="assets/js/tailwind.min.js"></script>
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <!-- Marked.js for Markdown rendering -->
    <script src="assets/js/marked.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hover-scale {
            transition: transform 0.2s ease-in-out;
        }

        .hover-scale:hover {
            transform: scale(1.02);
        }

        .copyable {
            cursor: pointer;
            border-bottom: 1px dashed #9CA3AF;
            transition: all 0.2s ease-in-out;
            position: relative;
        }

        .copyable:hover {
            background-color: #F3F4F6;
            border-bottom-color: #3B82F6;
        }

        .dark .copyable:hover {
            background-color: #374151;
        }

        .copy-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #1F2937;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease-in-out;
            z-index: 1000;
        }

        .copy-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: #1F2937;
        }

        .copyable:hover .copy-tooltip {
            opacity: 1;
        }

        /* Markdown 渲染样式 */
        .markdown-content {
            line-height: 1.6;
        }

        .markdown-content h2 {
            font-size: 1.1em;
            font-weight: 600;
            margin: 0.8em 0 0.4em 0;
            color: #1f2937;
        }

        .dark .markdown-content h2 {
            color: #f9fafb;
        }

        .markdown-content h3 {
            font-size: 1em;
            font-weight: 500;
            margin: 0.6em 0 0.3em 0;
            color: #374151;
        }

        .dark .markdown-content h3 {
            color: #e5e7eb;
        }

        .markdown-content ul {
            margin: 0.5em 0;
            padding-left: 1.2em;
        }

        .markdown-content li {
            margin: 0.2em 0;
        }

        .markdown-content strong {
            font-weight: 600;
            color: #111827;
        }

        .dark .markdown-content strong {
            color: #f3f4f6;
        }

        .markdown-content del {
            background-color: #fecaca;
            color: #991b1b;
            text-decoration: line-through;
            padding: 0 2px;
            border-radius: 2px;
        }

        .dark .markdown-content del {
            background-color: #7f1d1d;
            color: #fca5a5;
        }

        .markdown-content code {
            background-color: #f3f4f6;
            color: #1f2937;
            padding: 1px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .dark .markdown-content code {
            background-color: #374151;
            color: #e5e7eb;
        }

        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .dark ::-webkit-scrollbar-track {
            background: #374151;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .dark ::-webkit-scrollbar-thumb {
            background: #6b7280;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* 模态框滚动锁定 */
        .modal-open {
            overflow: hidden;
        }

        /* 企业选择列表的特殊滚动条 */
        #modalCompanySelectionList::-webkit-scrollbar {
            width: 6px;
        }

        #modalCompanySelectionList::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        #modalCompanySelectionList::-webkit-scrollbar-thumb {
            background: #10b981;
            border-radius: 3px;
        }

        #modalCompanySelectionList::-webkit-scrollbar-thumb:hover {
            background: #059669;
        }

        .dark #modalCompanySelectionList::-webkit-scrollbar-track {
            background: #374151;
        }

        .dark #modalCompanySelectionList::-webkit-scrollbar-thumb {
            background: #34d399;
        }

        .dark #modalCompanySelectionList::-webkit-scrollbar-thumb:hover {
            background: #10b981;
        }

        /* 确保企业选择区域有正确的高度和滚动 */
        #companySelectionColumn {
            /* 移除固定高度限制，让内容自然适应 */
        }

        #modalCompanySelectionList {
            max-height: 500px;
        }

        /* 企业选择列表中的企业卡片宽度调整 */
        #modalCompanySelectionList > div {
            width: calc(100% - 5px);
        }

        /* API返回数据详情左侧滚动条 */
        .api-left-column {
            overflow-y: auto;
            max-height: calc(100vh - 300px);
        }

        .api-left-column::-webkit-scrollbar {
            width: 6px;
        }

        .api-left-column::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .api-left-column::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-radius: 3px;
        }

        .api-left-column::-webkit-scrollbar-thumb:hover {
            background: #4b5563;
        }

        .dark .api-left-column::-webkit-scrollbar-track {
            background: #374151;
        }

        .dark .api-left-column::-webkit-scrollbar-thumb {
            background: #9ca3af;
        }

        .dark .api-left-column::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }

        /* 查询结果详情区域滚动条 */
        #queryResultsContent::-webkit-scrollbar {
            width: 6px;
        }

        #queryResultsContent::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-radius: 3px;
        }

        #queryResultsContent::-webkit-scrollbar-thumb:hover {
            background: #4b5563;
        }

        .dark #queryResultsContent::-webkit-scrollbar-thumb {
            background: #9ca3af;
        }

        .dark #queryResultsContent::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }

        /* AI分析内容滚动条 */
        #aiAnalysisContent::-webkit-scrollbar {
            width: 4px;
        }

        #aiAnalysisContent::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 2px;
        }

        #aiAnalysisContent::-webkit-scrollbar-thumb:hover {
            background: #2563eb;
        }

        .dark #aiAnalysisContent::-webkit-scrollbar-thumb {
            background: #60a5fa;
        }

        .dark #aiAnalysisContent::-webkit-scrollbar-thumb:hover {
            background: #3b82f6;
        }

        /* SQL示例代码块样式 */
        pre code {
            font-family: 'Courier New', Courier, monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* SQL示例模态框样式 */
        #sqlExamplesModal {
            max-height: 90vh;
        }

        #sqlExamplesModal pre {
            font-size: 11px;
            line-height: 1.3;
        }

        #sqlExamplesModal .modal-content {
            max-height: calc(90vh - 2rem);
        }

    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
<!-- 导航栏 -->
<nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <div class="flex items-center">
                <i class="fas fa-search-dollar text-2xl text-primary-600 mr-3"></i>
                <h1 class="text-xl font-semibold text-gray-900 dark:text-white">天眼查企业信息匹配系统</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="openUserManual()"
                        class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="fas fa-book mr-2"></i>
                    操作手册
                </button>
                <button id="themeToggle"
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:inline"></i>
                </button>
            </div>
        </div>
    </div>
</nav>

<!-- 主要内容 -->
<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 功能选择卡片 -->
    <div class="grid md:grid-cols-2 gap-6 mb-8">
        <!-- 匹配检查卡片 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover-scale">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-play text-primary-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">执行匹配检查</h3>
                    <p class="text-gray-600 dark:text-gray-400">启动企业信息匹配任务</p>
                </div>
            </div>
            <button onclick="showCheckForm()"
                    class="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                开始检查
            </button>
        </div>

        <!-- 日志分析卡片 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover-scale">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-chart-bar text-green-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志分析</h3>
                    <p class="text-gray-600 dark:text-gray-400">分析匹配结果日志</p>
                </div>
            </div>
            <button onclick="showAnalysisPanel()"
                    class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                分析日志
            </button>
        </div>
    </div>

    <!-- 匹配检查表单 -->
    <div id="checkForm"
         class="hidden bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8 fade-in">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-cog mr-2"></i>配置匹配检查参数
                </h3>
                <p id="configStatus" class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    配置将自动保存，下次打开时恢复
                </p>
            </div>
            <button onclick="hideCheckForm()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="checkFormData" class="space-y-4">
            <!-- 数据库类型 - 平铺单选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">数据库类型</label>
                <div class="flex space-x-6">
                    <label class="flex items-center">
                        <input type="radio" name="db" value="mysql" required
                               class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">MySQL</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="db" value="oracle" required
                               class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Oracle</span>
                    </label>
                </div>
            </div>

            <div>
                <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">查询SQL</label>
                    <button type="button" onclick="showSqlExamplesModal()"
                            class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                        <i class="fas fa-code mr-1"></i>查看示例
                    </button>
                </div>
                <textarea id="sqlTextarea" name="sql" required rows="6" placeholder="请输入查询SQL语句"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"></textarea>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <i class="fas fa-info-circle mr-1"></i>
                    查询SQL语句，必须使用别名来区分字段（ID: 业务主键，CODE: 社会信用代码，NAME: 名称，IF_SUB_ORG:
                    是否分支机构，DEL_MARK: 删除标记），SQL中可使用占位符（#{ID}、#{RID}）进行分页查询。
                </p>
            </div>

            <!-- 表名 - 移至SQL下方 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">表名</label>
                <input type="text" id="tableNameInput" name="tableName" required placeholder="例如: TB_TJ_CRPT"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <i class="fas fa-magic mr-1"></i>
                    表名会根据SQL自动填充，也可手动输入
                </p>
            </div>

            <!-- 字段列名配置 -->
            <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-2">
                    <i class="fas fa-columns mr-2"></i>字段列名配置
                </h4>
                <p class="text-xs text-blue-600 dark:text-blue-400 mb-4">
                    <i class="fas fa-info-circle mr-1"></i>
                    请填写数据库表的真实列名，如果不填写则自动从SQL中解析
                </p>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">业务主键列名</label>
                        <input type="text" id="businessKeyColumnInput" name="businessKeyColumn"
                               placeholder="真实列名，如: RID"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">社会信用代码列名</label>
                        <input type="text" id="creditCodeColumnInput" name="creditCodeColumn"
                               placeholder="真实列名，如: INSTITUTION_CODE"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">单位名称列名</label>
                        <input type="text" id="companyNameColumnInput" name="companyNameColumn"
                               placeholder="真实列名，如: CRPT_NAME"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">是否分支机构列名</label>
                        <input type="text" id="subOrgColumnInput" name="subOrgColumn"
                               placeholder="真实列名，如: IF_SUB_ORG"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标删标记列名</label>
                        <input type="text" id="delMarkColumnInput" name="delMarkColumn"
                               placeholder="真实列名，如: DEL_MARK"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    <i class="fas fa-lightbulb mr-1"></i>
                    系统会根据SQL自动解析字段列名，也可手动指定。列名不区分大小写。
                </p>
            </div>

            <!-- 更新SQL配置 -->
            <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-md font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-edit mr-2"></i>更新SQL配置
                    </h4>
                    <button type="button" onclick="showUpdateSqlExamplesModal()"
                            class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                        <i class="fas fa-code mr-1"></i>查看更新SQL示例
                    </button>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">更新SQL语句</label>
                    <textarea id="updateSqlTextarea" name="updateSql" rows="4"
                              placeholder="请输入更新SQL语句，多个语句用分号(;)分隔&#10;留空将根据查询SQL自动生成更新语句"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"></textarea>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <i class="fas fa-info-circle mr-1"></i>
                        支持占位符：表名称{tableName}、新社会信用代码{newCreditCode}、原社会信用代码{oldCreditCode}、新单位名称{newCompanyName}、原单位名称{oldCompanyName}、主键值{businessKey}
                    </p>
                </div>
            </div>

            <!-- AI分析选项 -->
            <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                <div class="flex items-center mb-2">
                    <input type="checkbox" id="useAi" name="useAi"
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="useAi" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        启用AI分析匹配失败原因
                    </label>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 ml-6">
                    AI分析参数已在配置文件中设置，请确保 application.yml 中的 ai.analysis 配置正确
                </p>
            </div>

            <div class="flex justify-between items-center">
                <button type="button" onclick="clearConfig()"
                        class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                        title="清除所有配置缓存">
                    <i class="fas fa-trash-alt mr-1"></i>清除配置
                </button>

                <div class="flex space-x-3">
                    <button type="button" onclick="hideCheckForm()"
                            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                        取消
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-play mr-2"></i>开始检查
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- 日志分析面板 -->
    <div id="analysisPanel"
         class="hidden bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8 fade-in">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-chart-line mr-2"></i>日志分析
            </h3>
            <button onclick="hideAnalysisPanel()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- 自动刷新控制 -->
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-4">
                <label class="flex items-center">
                    <input type="checkbox" id="autoRefreshCheckbox"
                           class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">自动刷新</span>
                </label>
                <select id="refreshInterval"
                        class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        disabled>
                    <option value="5000" selected>5秒</option>
                    <option value="10000">10秒</option>
                    <option value="30000">30秒</option>
                    <option value="60000">1分钟</option>
                </select>
            </div>
            <div id="refreshStatus" class="text-sm text-gray-500 dark:text-gray-400"></div>
        </div>

        <!-- 日志文件选择和过滤 -->
        <div class="grid md:grid-cols-5 gap-4 mb-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">日志文件</label>
                <select id="logFileSelect"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">请选择日志文件</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">匹配标识</label>
                <select id="matchTagFilter"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">全部</option>
                    <option value="1">全部匹配成功</option>
                    <option value="2">社会信用代码匹配成功</option>
                    <option value="3">单位名称匹配成功</option>
                    <option value="4">全部匹配失败</option>
                    <option value="5">不是同一个单位</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">处理状态</label>
                <select id="processedFilter"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">全部</option>
                    <option value="processed">已处理</option>
                    <option value="unprocessed">待处理</option>
                    <option value="success">匹配成功</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">关键词搜索</label>
                <div class="relative">
                    <input type="text" id="keywordFilter" placeholder="搜索企业名称、社会信用代码或业务主键"
                           class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <button id="clearKeywordBtn" onclick="clearKeywordSearch()"
                            class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded transition-colors hidden"
                            title="清空搜索">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>
            <div class="flex items-end space-x-2">
                <button onclick="analyzeLog()"
                        class="flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>分析
                </button>
                <button onclick="exportResults()"
                        class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        </div>

        <!-- 配置信息展示 -->
        <div id="configInfoPanel" class="hidden mb-6">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 flex items-center">
                        <i class="fas fa-cog mr-2"></i>匹配检查配置信息
                    </h4>
                    <button onclick="editUpdateSql()"
                            class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors">
                        <i class="fas fa-edit mr-1"></i>编辑更新SQL
                    </button>
                </div>
                <div id="configInfoContent" class="grid md:grid-cols-2 gap-4 text-sm">
                    <!-- 配置信息内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div id="statisticsPanel" class="hidden mb-6">
            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">匹配统计</h4>
            <div id="statisticsCards" class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                <!-- 统计卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 分析结果表格 -->
        <div id="resultsTable" class="hidden">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white">匹配结果</h4>
                <span id="resultCount" class="text-sm text-gray-600 dark:text-gray-400"></span>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            业务主键
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            单位名称
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            社会信用代码
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            匹配标识
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            处理状态
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            操作
                        </th>
                    </tr>
                    </thead>
                    <tbody id="resultsTableBody"
                           class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- 表格内容将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div id="paginationContainer" class="hidden mt-4 flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    显示第 <span id="pageStart">1</span> - <span id="pageEnd">20</span> 条，共 <span
                        id="totalCount">0</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button id="prevPageBtn" onclick="changePage(-1)"
                            class="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>
                    <span id="pageInfo" class="text-sm text-gray-700 dark:text-gray-300">第 1 页，共 1 页</span>
                    <button id="nextPageBtn" onclick="changePage(1)"
                            class="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999]"></div>
</main>

<!-- API数据查看模态框 -->
<div id="jsonModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="relative mx-auto p-5 border w-11/12 max-w-7xl h-5/6 shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col">
        <div class="flex-shrink-0">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="jsonModalTitle">API返回数据详情</h3>
                <button onclick="closeJsonModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 两列布局 -->
            <div id="modalContentGrid" class="grid grid-cols-1 gap-6 mt-4 flex-1 min-h-0">
                <!-- 左列：原始信息和查询结果 -->
                <div class="flex flex-col space-y-4 min-h-0 overflow-y-auto api-left-column">
                    <!-- 原始数据信息 -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800 flex-shrink-0">
                        <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 mb-4 flex items-center">
                            <i class="fas fa-info-circle mr-2"></i>原始查询信息
                        </h4>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0 w-20 text-sm font-medium text-blue-700 dark:text-blue-300">
                                    业务主键
                                </div>
                                <div id="modalBusinessKey"
                                     class="copyable flex-1 text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 px-3 py-2 rounded-md border border-blue-200 dark:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-800/30 transition-colors cursor-pointer"
                                     onclick="copyToClipboard(this.textContent, this)" title="点击复制">
                                    -
                                    <span class="copy-tooltip">点击复制</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0 w-20 text-sm font-medium text-blue-700 dark:text-blue-300">
                                    单位名称
                                </div>
                                <div id="modalCompanyName"
                                     class="copyable flex-1 text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 px-3 py-2 rounded-md border border-blue-200 dark:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-800/30 transition-colors cursor-pointer"
                                     onclick="copyToClipboard(this.textContent, this)" title="点击复制">
                                    -
                                    <span class="copy-tooltip">点击复制</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0 w-20 text-sm font-medium text-blue-700 dark:text-blue-300">
                                    信用代码
                                </div>
                                <div id="modalCreditCode"
                                     class="copyable flex-1 text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 px-3 py-2 rounded-md border border-blue-200 dark:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-800/30 transition-colors cursor-pointer"
                                     onclick="copyToClipboard(this.textContent, this)" title="点击复制">
                                    -
                                    <span class="copy-tooltip">点击复制</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI分析结果 -->
                    <div id="aiAnalysisSection"
                         class="hidden bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800 flex-shrink-0">
                        <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 mb-2 flex items-center">
                            <i class="fas fa-robot mr-2"></i>AI分析结果
                        </h4>
                        <div id="aiAnalysisContent"
                             class="text-sm text-blue-800 dark:text-blue-200 markdown-content max-h-32 overflow-y-auto">
                            <!-- AI分析内容将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 查询结果详情按钮 -->
                    <div class="flex-shrink-0">
                        <button onclick="showQueryResultsModal()"
                                class="w-full flex items-center justify-center p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors">
                            <i class="fas fa-search-plus mr-2 text-gray-500 dark:text-gray-400"></i>
                            <span class="text-md font-semibold text-gray-900 dark:text-white">查看查询结果详情</span>
                        </button>
                    </div>
                </div>

                <!-- 右列：企业选择区域 -->
                <div id="companySelectionColumn" class="hidden flex flex-col min-h-0">
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800 flex flex-col h-full min-h-0">
                        <h4 class="text-md font-semibold text-green-900 dark:text-green-100 mb-4 flex items-center flex-shrink-0">
                            <i class="fas fa-building mr-2"></i>选择匹配企业
                        </h4>
                        <div id="modalCompanySelectionList" class="space-y-2 overflow-y-auto flex-1 min-h-0">
                            <!-- 企业选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 查询结果详情模态框 -->
<div id="queryResultsModal"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="relative mx-auto p-5 border w-11/12 max-w-6xl h-5/6 shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col">
        <div class="flex-shrink-0">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">查询结果详情</h3>
                <button onclick="closeQueryResultsModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 标签页 -->
            <div class="flex border-b border-gray-200 dark:border-gray-600 mt-4">
                <button id="creditCodeTabModal" onclick="switchJsonTabModal('creditCode')"
                        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b-2 border-primary-500 text-primary-600 dark:text-primary-400">
                    社会信用代码查询
                </button>
                <button id="companyNameTabModal" onclick="switchJsonTabModal('companyName')"
                        class="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-700 dark:hover:text-gray-300">
                    单位名称查询
                </button>
            </div>
        </div>

        <!-- 查询结果内容 -->
        <div class="flex-1 min-h-0 overflow-y-auto mt-4">
            <!-- 社会信用代码查询结果 -->
            <div id="creditCodeContentModal" class="api-content">
                <!-- 解析后的表单信息 -->
                <div class="mb-4">
                    <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">查询结果解析</h5>
                    <div id="creditCodeParsedInfoModal" class="space-y-3">
                        <!-- 动态生成的解析信息 -->
                    </div>
                </div>

                <!-- JSON原始数据 -->
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div class="bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-t-lg border-b border-gray-200 dark:border-gray-600">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">原始JSON数据</span>
                    </div>
                    <div id="creditCodeJsonSectionModal">
                        <pre id="creditCodeJsonContentModal"
                             class="bg-gray-100 dark:bg-gray-800 p-4 rounded-b-lg overflow-auto max-h-96 text-xs text-gray-900 dark:text-gray-100"></pre>
                    </div>
                </div>
            </div>

            <!-- 单位名称查询结果 -->
            <div id="companyNameContentModal" class="api-content hidden">
                <!-- 解析后的表单信息 -->
                <div class="mb-4">
                    <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">查询结果解析</h5>
                    <div id="companyNameParsedInfoModal" class="space-y-3">
                        <!-- 动态生成的解析信息 -->
                    </div>
                </div>

                <!-- JSON原始数据 -->
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div class="bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-t-lg border-b border-gray-200 dark:border-gray-600">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">原始JSON数据</span>
                    </div>
                    <div id="companyNameJsonSectionModal">
                        <pre id="companyNameJsonContentModal"
                             class="bg-gray-100 dark:bg-gray-800 p-4 rounded-b-lg overflow-auto max-h-96 text-xs text-gray-900 dark:text-gray-100"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模态框底部 -->
        <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-600 mt-4 flex-shrink-0">
            <div class="flex space-x-3">
                <button onclick="copyCurrentJsonToClipboard()"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-copy mr-2"></i>复制当前JSON
                </button>
                <button onclick="closeQueryResultsModal()"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors">
                    关闭
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 企业选择确认模态框 -->
<div id="companyConfirmModal"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="relative mx-auto p-6 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="flex flex-col">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-check-circle mr-2 text-green-500"></i>确认选择企业
                </h3>
                <button onclick="closeCompanyConfirmModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 信息对比展示 -->
            <div class="mt-4 space-y-4">
                <!-- 选择的企业信息 -->
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                    <!--<h4 class="text-md font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                            <i class="fas fa-building mr-2"></i>选择的企业信息
                        </h4>-->
                    <div id="confirmCompanyInfo" class="space-y-2">
                        <!-- 企业信息将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 变更提示 -->
                <div id="changesHighlight"
                     class="hidden bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <h4 class="text-md font-semibold text-yellow-900 dark:text-yellow-100 mb-3 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>信息变更提示
                    </h4>
                    <div id="changesList" class="space-y-1">
                        <!-- 变更信息将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 更新SQL语句显示 -->
                <div id="updateSqlDisplay"
                     class="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                    <h4 class="text-md font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
                        <i class="fas fa-database mr-2"></i>将要执行的更新SQL
                    </h4>
                    <div id="updateSqlContent"
                         class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm font-mono text-gray-900 dark:text-gray-100 overflow-x-auto">
                        <!-- 更新SQL将通过JavaScript动态生成 -->
                    </div>
                    <div class="mt-2 flex justify-end">
                        <button onclick="copyUpdateSql()"
                                class="text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded transition-colors">
                            <i class="fas fa-copy mr-1"></i>复制SQL
                        </button>
                    </div>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-600 mt-4">
                <div class="flex space-x-3">
                    <button onclick="closeCompanyConfirmModal()"
                            class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors">
                        <i class="fas fa-times mr-2"></i>取消
                    </button>
                    <button onclick="confirmCompanySelection()"
                            class="bg-green-500 hover:bg-green-700 text-white font-medium py-2 px-4 rounded transition-colors">
                        <i class="fas fa-check mr-2"></i>确认选择
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑更新SQL模态框 -->
<div id="editUpdateSqlModal"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
    <div class="relative mx-auto p-6 border w-full max-w-4xl max-h-full shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col modal-content">
        <div class="flex-shrink-0">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-edit mr-2 text-primary-600"></i>编辑更新SQL
                </h3>
                <button onclick="closeEditUpdateSqlModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- 编辑内容 -->
        <div class="flex-1 min-h-0 overflow-y-auto mt-4 custom-scrollbar">
            <div class="space-y-4">
                <!-- 当前配置信息 -->
                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                        <i class="fas fa-info-circle mr-2"></i>当前配置信息
                    </h4>
                    <div id="currentConfigInfo"
                         class="grid md:grid-cols-2 gap-4 text-sm text-blue-800 dark:text-blue-200">
                        <!-- 当前配置信息将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 更新SQL编辑器 -->
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-md font-semibold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-database mr-2"></i>更新SQL模板
                        </h4>
                        <button onclick="resetUpdateSql()"
                                class="text-xs bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded transition-colors">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                    </div>
                    <textarea id="editUpdateSqlTextarea" rows="8"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm resize-none"
                              placeholder="请输入更新SQL模板..."></textarea>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        支持占位符：表名称{tableName}、新社会信用代码{newCreditCode}、原社会信用代码{oldCreditCode}、新单位名称{newCompanyName}、原单位名称{oldCompanyName}、主键值{businessKey}
                    </p>
                </div>

                <!-- 预览区域 -->
                <div id="sqlPreviewSection"
                     class="hidden bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                    <h4 class="text-md font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                        <i class="fas fa-eye mr-2"></i>SQL预览（示例数据）
                    </h4>
                    <pre id="sqlPreviewContent"
                         class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm text-gray-900 dark:text-gray-100 overflow-x-auto max-h-40 overflow-y-auto"></pre>
                    <button onclick="previewUpdateSql()"
                            class="mt-2 text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors">
                        <i class="fas fa-refresh mr-1"></i>刷新预览
                    </button>
                </div>
            </div>
        </div>

        <!-- 模态框底部 -->
        <div class="flex justify-between pt-4 border-t border-gray-200 dark:border-gray-600 mt-4 flex-shrink-0">
            <button onclick="previewUpdateSql()"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors">
                <i class="fas fa-eye mr-2"></i>预览SQL
            </button>
            <div class="flex space-x-3">
                <button onclick="closeEditUpdateSqlModal()"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button onclick="saveUpdateSql()"
                        class="bg-green-500 hover:bg-green-700 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-save mr-2"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 更新SQL示例模态框 -->
<div id="updateSqlExamplesModal"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
    <div class="relative mx-auto p-6 border w-full max-w-4xl max-h-full shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col modal-content">
        <div class="flex-shrink-0">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-edit mr-2 text-purple-600"></i>更新SQL示例
                </h3>
                <button onclick="closeUpdateSqlExamplesModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- 示例内容 -->
        <div class="flex-1 min-h-0 overflow-y-auto mt-4 custom-scrollbar">
            <!-- 更新SQL示例区域 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-edit mr-2 text-purple-600"></i>更新SQL示例
                </h3>
                <div class="space-y-4">
                    <!-- 基础更新SQL示例 -->
                    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-semibold text-purple-900 dark:text-purple-100 flex items-center">
                                <i class="fas fa-edit mr-2"></i>职卫数据库(Oracle)
                            </h4>
                            <button onclick="selectUpdateSqlExample('update-basic')"
                                    class="text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded transition-colors">
                                <i class="fas fa-check mr-1"></i>选择
                            </button>
                        </div>
                        <pre id="updateBasicSqlExample"
                             class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm text-gray-900 dark:text-gray-100 overflow-x-auto max-h-40 overflow-y-auto"><code>
UPDATE TB_TJ_CRPT SET
    INSTITUTION_CODE = '{newCreditCode}',
    CRPT_NAME = '{newCompanyName}'
WHERE RID = '{businessKey}';
UPDATE TB_TJ_CRPT SET
    INSTITUTION_CODE = '{newCreditCode}'
WHERE IF_SUB_ORG = 1 AND INSTITUTION_CODE = '{oldCreditCode}';
                        </code></pre>
                    </div>

                    <!-- 监管数据库(Mysql)(江苏)示例 -->
                    <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-semibold text-orange-900 dark:text-orange-100 flex items-center">
                                <i class="fas fa-cogs mr-2"></i>监管数据库(Mysql)(江苏)
                            </h4>
                            <button onclick="selectUpdateSqlExample('update-advanced')"
                                    class="text-xs bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded transition-colors">
                                <i class="fas fa-check mr-1"></i>选择
                            </button>
                        </div>
                        <pre id="updateAdvancedSqlExample"
                             class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm text-gray-900 dark:text-gray-100 overflow-x-auto max-h-40 overflow-y-auto"><code>
UPDATE TD_EMPLOYER_BASIC
SET CREDIT_CODE = '{newCreditCode}',
    UNIT_NAME = '{newCompanyName}'
WHERE RID = '{businessKey}';
UPDATE TD_EMPLOYER_BASIC
SET CREDIT_CODE = '{newCreditCode}'
WHERE IF_BRANCH = 1 AND RID = '{oldCreditCode}';
                        </code></pre>
                    </div>
                </div>
            </div>

            <!-- 说明文档 -->
            <div class="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>更新SQL编写说明
                </h5>
                <div class="space-y-4">
                    <div>
                        <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                            <i class="fas fa-edit mr-1 text-purple-500"></i>更新SQL说明
                        </h6>
                        <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1 ml-4">
                            <li><strong>占位符：</strong>{newCreditCode}、{newCompanyName}、{businessKey} 等</li>
                            <li><strong>历史记录：</strong>可选择包含变更历史记录的高级更新SQL</li>
                            <li><strong>多语句：</strong>支持多条SQL语句，用分号分隔</li>
                            <li><strong>表名：</strong>使用{tableName}占位符自动替换表名</li>
                            <li><strong>字段名：</strong>使用真实的数据库列名，不是别名</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模态框底部 -->
        <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-600 mt-4 flex-shrink-0">
            <button onclick="closeUpdateSqlExamplesModal()"
                    class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors">
                <i class="fas fa-times mr-2"></i>关闭
            </button>
        </div>
    </div>
</div>

<!-- SQL示例模态框 -->
<div id="sqlExamplesModal"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
    <div class="relative mx-auto p-6 border w-full max-w-4xl max-h-full shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col modal-content">
        <div class="flex-shrink-0">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-code mr-2 text-primary-600"></i>查询SQL示例
                </h3>
                <button onclick="closeSqlExamplesModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- 示例内容 -->
        <div class="flex-1 min-h-0 overflow-y-auto mt-4 custom-scrollbar">
            <!-- 查询SQL示例区域 -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-search mr-2 text-blue-600"></i>查询SQL示例
                </h3>
                <div class="space-y-4">
                    <!-- Oracle查询示例 -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 flex items-center">
                                <i class="fas fa-database mr-2"></i>职卫数据库(Oracle)
                            </h4>
                            <button onclick="selectSqlExample('oracle')"
                                    class="text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors">
                                <i class="fas fa-check mr-1"></i>选择
                            </button>
                        </div>
                        <pre id="oracleSqlExample"
                             class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm text-gray-900 dark:text-gray-100 overflow-x-auto max-h-40 overflow-y-auto"><code>
SELECT
    INSTITUTION_CODE AS CODE,
    CRPT_NAME AS NAME,
    RID AS ID,
    DEL_MARK AS DEL_MARK,
    IF_SUB_ORG AS IF_SUB_ORG
FROM TB_TJ_CRPT
WHERE IF_SUB_ORG = 0
    AND RID > #{ID}
    AND INSTITUTION_CODE IS NOT NULL
    AND CRPT_NAME IS NOT NULL
ORDER BY RID
                        </code></pre>
                    </div>

                    <!-- MySQL查询示例 -->
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-semibold text-green-900 dark:text-green-100 flex items-center">
                                <i class="fas fa-database mr-2"></i>监管数据库(Mysql)(江苏)
                            </h4>
                            <button onclick="selectSqlExample('mysql')"
                                    class="text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors">
                                <i class="fas fa-check mr-1"></i>选择
                            </button>
                        </div>
                        <pre id="mysqlSqlExample"
                             class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm text-gray-900 dark:text-gray-100 overflow-x-auto max-h-40 overflow-y-auto"><code>
SELECT B.CREDIT_CODE AS CODE,
       B.UNIT_NAME AS NAME,
       B.RID AS ID,
       B.IF_DELETE AS DEL_MARK,
       B.IF_BRANCH AS IF_SUB_ORG
FROM TD_EMPLOYER_BASIC B
         LEFT JOIN TD_EMPLOYER_BASIC P ON B.PARENT_UNIT_UUID = P.EMPLOYER_UUID
WHERE (B.IF_BRANCH IS NULL OR B.IF_BRANCH = 0 OR (B.IF_BRANCH = 1 AND P.CREDIT_CODE <> B.CREDIT_CODE))
  AND B.RID > #{ID}
  AND B.CREDIT_CODE IS NOT NULL
  AND B.UNIT_NAME IS NOT NULL
ORDER BY B.RID
                        </code></pre>
                    </div>
                </div>
            </div>



            <!-- 说明文档 -->
            <div class="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h5 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>SQL编写说明
                </h5>
                <div class="space-y-4">
                    <div>
                        <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                            <i class="fas fa-exclamation-triangle mr-1 text-orange-500"></i>重要：查询SQL必须使用AS别名
                        </h6>
                        <p class="text-sm text-gray-700 dark:text-gray-300 mb-2">
                            查询SQL必须使用AS关键字为字段指定别名，系统会根据别名自动识别字段类型。
                        </p>
                        <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1 ml-4">
                            <li><strong>CODE</strong> - 社会信用代码字段的别名</li>
                            <li><strong>NAME</strong> - 单位名称字段的别名</li>
                            <li><strong>ID</strong> - 业务主键字段的别名</li>
                            <li><strong>DEL_MARK</strong> - 标删标记字段</li>
                            <li><strong>IF_SUB_ORG</strong> - 是否分支机构字段</li>
                        </ul>
                    </div>
                    <div>
                        <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                            <i class="fas fa-cogs mr-1 text-blue-500"></i>查询SQL要求
                        </h6>
                        <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1 ml-4">
                            <li><strong>占位符：</strong>#{ID}、#{id}、#{RID}、#{rid} 等，用于分页查询</li>
                            <li><strong>排序：</strong>建议按业务主键字段排序，确保分页查询的一致性</li>
                        </ul>
                    </div>

                </div>
            </div>
        </div>

        <!-- 模态框底部 -->
        <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-600 mt-4 flex-shrink-0">
            <button onclick="closeSqlExamplesModal()"
                    class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors">
                <i class="fas fa-times mr-2"></i>关闭
            </button>
        </div>
    </div>
</div>

<!-- 页脚 -->
<footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
            <div class="flex justify-center items-center space-x-6 mb-4">
                <a href="https://github.com" target="_blank"
                   class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                    <i class="fab fa-github text-xl"></i>
                </a>
                <a href="https://twitter.com" target="_blank"
                   class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                    <i class="fab fa-twitter text-xl"></i>
                </a>
                <a href="https://linkedin.com" target="_blank"
                   class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                    <i class="fab fa-linkedin text-xl"></i>
                </a>
            </div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
                天眼查企业信息匹配系统.
            </p>
        </div>
    </div>
</footer>

<script>
    // 主题切换
    const themeToggle = document.getElementById('themeToggle');
    const html = document.documentElement;

    // 初始化主题
    if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        html.classList.add('dark');
    } else {
        html.classList.remove('dark');
    }

    themeToggle.addEventListener('click', () => {
        html.classList.toggle('dark');
        localStorage.theme = html.classList.contains('dark') ? 'dark' : 'light';
    });

    // API基础URL
    const API_BASE = '';

    // 自动刷新相关变量
    let autoRefreshTimer = null;
    let currentLogFileName = '';
    let currentMatchResults = []; // 存储当前的匹配结果数据

    // 分析状态管理
    let isAnalyzing = false;

    // 分页相关变量
    let currentPage = 1;
    let pageSize = 20;
    let totalRecords = 0;
    let filteredResults = []; // 过滤后的结果

    // 配置缓存相关
    const CONFIG_CACHE_KEY = 'crpt_check_config';
    const CONFIG_CACHE_VERSION = '1.0';

    // 配置缓存管理
    function saveConfig() {
        try {
            const config = {
                version: CONFIG_CACHE_VERSION,
                timestamp: new Date().getTime(),
                data: {
                    db: document.querySelector('input[name="db"]:checked')?.value || '',
                    tableName: document.getElementById('tableNameInput')?.value || '',
                    sql: document.getElementById('sqlTextarea')?.value || '',
                    creditCodeColumn: document.getElementById('creditCodeColumnInput')?.value || '',
                    companyNameColumn: document.getElementById('companyNameColumnInput')?.value || '',
                    businessKeyColumn: document.getElementById('businessKeyColumnInput')?.value || '',
                    delMarkColumn: document.getElementById('delMarkColumnInput')?.value || '',
                    subOrgColumn: document.getElementById('subOrgColumnInput')?.value || '',
                    updateSql: document.getElementById('updateSqlTextarea')?.value || '',
                    useAi: document.getElementById('useAi')?.checked || false
                }
            };

            localStorage.setItem(CONFIG_CACHE_KEY, JSON.stringify(config));
            console.log('配置已保存到缓存');

            // 更新状态提示
            const configStatus = document.getElementById('configStatus');
            if (configStatus) {
                configStatus.textContent = '配置已自动保存';
                configStatus.className = 'text-sm text-blue-600 dark:text-blue-400 mt-1';

                // 3秒后恢复默认提示
                setTimeout(() => {
                    configStatus.textContent = '配置将自动保存，下次打开时恢复';
                    configStatus.className = 'text-sm text-gray-500 dark:text-gray-400 mt-1';
                }, 3000);
            }
        } catch (error) {
            console.warn('保存配置失败:', error);
        }
    }

    function loadConfig() {
        try {
            const cached = localStorage.getItem(CONFIG_CACHE_KEY);
            if (!cached) {
                console.log('未找到缓存配置');
                return false;
            }

            const config = JSON.parse(cached);

            // 检查版本兼容性
            if (config.version !== CONFIG_CACHE_VERSION) {
                console.log('配置版本不兼容，清除缓存');
                localStorage.removeItem(CONFIG_CACHE_KEY);
                return false;
            }

            // 检查缓存时间（7天过期）
            const now = new Date().getTime();
            const cacheAge = now - config.timestamp;
            const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天

            if (cacheAge > maxAge) {
                console.log('配置缓存已过期，清除缓存');
                localStorage.removeItem(CONFIG_CACHE_KEY);
                return false;
            }

            // 恢复配置
            const data = config.data;

            // 基本配置
            if (data.db) {
                const dbRadio = document.querySelector(`input[name="db"][value="${data.db}"]`);
                if (dbRadio) {
                    dbRadio.checked = true;
                }
            }
            if (data.tableName && document.getElementById('tableNameInput')) {
                document.getElementById('tableNameInput').value = data.tableName;
            }
            if (data.sql && document.getElementById('sqlTextarea')) {
                document.getElementById('sqlTextarea').value = data.sql;
            }

            // 字段列名配置
            if (data.creditCodeColumn && document.getElementById('creditCodeColumnInput')) {
                document.getElementById('creditCodeColumnInput').value = data.creditCodeColumn;
            }
            if (data.companyNameColumn && document.getElementById('companyNameColumnInput')) {
                document.getElementById('companyNameColumnInput').value = data.companyNameColumn;
            }
            if (data.businessKeyColumn && document.getElementById('businessKeyColumnInput')) {
                document.getElementById('businessKeyColumnInput').value = data.businessKeyColumn;
            }
            if (data.delMarkColumn && document.getElementById('delMarkColumnInput')) {
                document.getElementById('delMarkColumnInput').value = data.delMarkColumn;
            }
            if (data.subOrgColumn && document.getElementById('subOrgColumnInput')) {
                document.getElementById('subOrgColumnInput').value = data.subOrgColumn;
            }

            // 更新SQL配置
            if (data.updateSql && document.getElementById('updateSqlTextarea')) {
                document.getElementById('updateSqlTextarea').value = data.updateSql;
            }

            // AI配置
            if (document.getElementById('useAi')) {
                document.getElementById('useAi').checked = data.useAi || false;
            }

            console.log('配置已从缓存恢复');

            // 更新状态提示
            const configStatus = document.getElementById('configStatus');
            if (configStatus) {
                const cacheDate = new Date(config.timestamp);
                configStatus.textContent = `已恢复上次配置 (${cacheDate.toLocaleString()})`;
                configStatus.className = 'text-sm text-green-600 dark:text-green-400 mt-1';
            }

            return true;

        } catch (error) {
            console.warn('加载配置失败:', error);
            localStorage.removeItem(CONFIG_CACHE_KEY);
            return false;
        }
    }

    function clearConfig() {
        try {
            localStorage.removeItem(CONFIG_CACHE_KEY);
            console.log('配置缓存已清除');

            // 重置表单
            document.getElementById('checkFormData')?.reset();


            // 更新状态提示
            const configStatus = document.getElementById('configStatus');
            if (configStatus) {
                configStatus.textContent = '配置已清除，重新开始';
                configStatus.className = 'text-sm text-orange-600 dark:text-orange-400 mt-1';

                // 3秒后恢复默认提示
                setTimeout(() => {
                    configStatus.textContent = '配置将自动保存，下次打开时恢复';
                    configStatus.className = 'text-sm text-gray-500 dark:text-gray-400 mt-1';
                }, 3000);
            }

            showMessage('配置已重置', 'success');
        } catch (error) {
            console.warn('清除配置失败:', error);
        }
    }

    // 显示/隐藏检查表单
    function showCheckForm() {
        document.getElementById('checkForm').classList.remove('hidden');
        document.getElementById('analysisPanel').classList.add('hidden');

        // 加载缓存配置
        loadConfig();
    }

    function hideCheckForm() {
        document.getElementById('checkForm').classList.add('hidden');
    }

    // 显示/隐藏分析面板
    function showAnalysisPanel() {
        document.getElementById('analysisPanel').classList.remove('hidden');
        document.getElementById('checkForm').classList.add('hidden');
        loadLogFiles(true); // 自动选择第一个文件
    }

    function hideAnalysisPanel() {
        document.getElementById('analysisPanel').classList.add('hidden');
        // 停止自动刷新
        stopAutoRefresh();
    }

    // 表名自动填充功能
    function autoFillTableName() {
        const sqlTextarea = document.getElementById('sqlTextarea');
        const tableNameInput = document.getElementById('tableNameInput');

        if (!sqlTextarea || !tableNameInput) return;

        const sql = sqlTextarea.value.trim();
        if (!sql || tableNameInput.value.trim()) return; // 如果SQL为空或表名已填写，则不自动填充

        // 改进的表名解析逻辑，支持复杂的FROM子句（包含JOIN）
        // 首先尝试匹配简单的FROM子句
        let fromMatch = sql.match(/FROM\s+([^\s\n\r,]+)/i);
        if (fromMatch && fromMatch[1]) {
            let tableName = fromMatch[1].trim();
            // 移除可能的别名（如果有AS关键字或空格后跟别名）
            tableName = tableName.split(/\s+/)[0];
            // 移除可能的数据库前缀（如schema.table）
            if (tableName.includes('.')) {
                tableName = tableName.split('.').pop();
            }
            tableNameInput.value = tableName;
            return;
        }

        // 如果简单匹配失败，尝试匹配包含JOIN的复杂FROM子句
        // 匹配 FROM table_name alias LEFT JOIN ... 的模式
        fromMatch = sql.match(/FROM\s+([^\s\n\r]+)\s+([A-Za-z]\w*)\s+(?:LEFT|RIGHT|INNER|FULL)?\s*JOIN/i);
        if (fromMatch && fromMatch[1]) {
            let tableName = fromMatch[1].trim();
            // 移除可能的数据库前缀（如schema.table）
            if (tableName.includes('.')) {
                tableName = tableName.split('.').pop();
            }
            tableNameInput.value = tableName;
        }
    }

    // SQL字段解析功能
    function parseFieldsFromSql() {
        const sqlTextarea = document.getElementById('sqlTextarea');
        if (!sqlTextarea) return;

        const sql = sqlTextarea.value.trim();
        if (!sql) return;

        // 解析SELECT子句中的字段
        const selectMatch = sql.match(/SELECT\s+(.*?)\s+FROM/is);
        if (!selectMatch) return;

        const selectClause = selectMatch[1];
        const fields = selectClause.split(',').map(field => field.trim());

        // 解析字段映射
        const fieldMappings = {};
        fields.forEach(field => {
            // 处理 AS 别名
            const asMatch = field.match(/(.+?)\s+AS\s+(\w+)/i);
            if (asMatch) {
                let originalColumn = asMatch[1].trim();
                const alias = asMatch[2].trim().toUpperCase();
                if (originalColumn.includes('.')) {
                    const parts = originalColumn.split('.');
                    if (parts.length === 2) {
                        originalColumn = parts[1].trim();
                    }
                }
                fieldMappings[alias] = originalColumn;
            } else {
                // 没有别名的情况，需要改进字段名解析
                let columnName = field.trim();
                if (columnName.includes('.')) {
                    const parts = columnName.split('.');
                    if (parts.length === 2) {
                        columnName = parts[1].trim();
                    }
                }
                const mappingKey = mapColumnToStandardAlias(columnName);
                fieldMappings[mappingKey] = columnName;
            }
        });

        // 自动填充字段列名（使用真实列名）
        const creditCodeInput = document.getElementById('creditCodeColumnInput');
        const companyNameInput = document.getElementById('companyNameColumnInput');
        const businessKeyInput = document.getElementById('businessKeyColumnInput');
        const delMarkInput = document.getElementById('delMarkColumnInput');
        const subOrgInput = document.getElementById('subOrgColumnInput');

        // 根据常见的别名映射填充字段（填充真实列名）
        if (fieldMappings['CODE'] && creditCodeInput && !creditCodeInput.value.trim()) {
            creditCodeInput.value = fieldMappings['CODE']; // 填充真实列名
        }
        if (fieldMappings['NAME'] && companyNameInput && !companyNameInput.value.trim()) {
            companyNameInput.value = fieldMappings['NAME']; // 填充真实列名
        }
        if (fieldMappings['ID'] && businessKeyInput && !businessKeyInput.value.trim()) {
            businessKeyInput.value = fieldMappings['ID']; // 填充真实列名
        }

        // 查找可能的标删标记字段
        if (fieldMappings['DEL_MARK'] && delMarkInput && !delMarkInput.value.trim()) {
            delMarkInput.value = fieldMappings['DEL_MARK']; // 填充真实列名
        }

        // 查找可能的分支机构字段
        if (fieldMappings['IF_SUB_ORG'] && subOrgInput && !subOrgInput.value.trim()) {
            subOrgInput.value = fieldMappings['IF_SUB_ORG']; // 填充真实列名
        }

        console.log('解析到的字段映射:', fieldMappings);

        // 移除自动生成更新SQL的功能
        // autoGenerateUpdateSqlIfEmpty();
    }

    // 将列名映射到标准别名
    function mapColumnToStandardAlias(columnName) {
        const upperColumnName = columnName.toUpperCase();

        // 社会信用代码字段映射
        if (upperColumnName.includes('CREDIT') && upperColumnName.includes('CODE')) {
            return 'CODE';
        }

        // 企业名称字段映射
        if (upperColumnName.includes('UNIT') && upperColumnName.includes('NAME')) {
            return 'NAME';
        }
        if (upperColumnName.includes('COMPANY') && upperColumnName.includes('NAME')) {
            return 'NAME';
        }
        if (upperColumnName.includes('CRPT') && upperColumnName.includes('NAME')) {
            return 'NAME';
        }

        // 业务主键字段映射
        if (upperColumnName === 'RID' || upperColumnName === 'ID') {
            return 'ID';
        }

        // 删除标记字段映射
        if (upperColumnName.includes('IF') && upperColumnName.includes('DELETE')) {
            return 'DEL_MARK';
        }
        if (upperColumnName.includes('DEL') && upperColumnName.includes('MARK')) {
            return 'DEL_MARK';
        }

        // 分支机构字段映射 - 增强识别逻辑
        // 优先匹配完整的字段名
        if (upperColumnName === 'IF_SUB_ORG') {
            return 'IF_SUB_ORG';
        }
        if (upperColumnName === 'IF_BRANCH') {
            return 'IF_SUB_ORG';
        }
        // 模糊匹配分支机构相关字段
        if (upperColumnName.includes('IF') && upperColumnName.includes('BRANCH')) {
            return 'IF_SUB_ORG';
        }
        if (upperColumnName.includes('SUB') && upperColumnName.includes('ORG')) {
            return 'IF_SUB_ORG';
        }
        // 支持更多分支机构字段模式
        if (upperColumnName.includes('BRANCH') && (upperColumnName.includes('FLAG') || upperColumnName.includes('MARK'))) {
            return 'IF_SUB_ORG';
        }
        if (upperColumnName.includes('SUB') && (upperColumnName.includes('COMPANY') || upperColumnName.includes('UNIT'))) {
            return 'IF_SUB_ORG';
        }

        // 默认使用原字段名作为键
        return upperColumnName;
    }



    // 从SQL中解析表名
    function parseTableNameFromSql(sql) {
        const fromMatch = sql.match(/FROM\s+([^\s\n\r]+)/i);
        if (fromMatch && fromMatch[1]) {
            let tableName = fromMatch[1].trim();
            tableName = tableName.split(/\s+/)[0];
            if (tableName.includes('.')) {
                tableName = tableName.split('.').pop();
            }
            return tableName;
        }
        return null;
    }

    // 显示SQL示例模态框
    function showSqlExamplesModal() {
        document.getElementById('sqlExamplesModal').classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 关闭SQL示例模态框
    function closeSqlExamplesModal() {
        document.getElementById('sqlExamplesModal').classList.add('hidden');
        document.body.classList.remove('modal-open');
    }

    // 显示更新SQL示例模态框
    function showUpdateSqlExamplesModal() {
        document.getElementById('updateSqlExamplesModal').classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 关闭更新SQL示例模态框
    function closeUpdateSqlExamplesModal() {
        document.getElementById('updateSqlExamplesModal').classList.add('hidden');
        document.body.classList.remove('modal-open');
    }

    // 选择SQL示例
    function selectSqlExample(type) {
        let sqlText = '';

        switch (type) {
            case 'oracle':
                sqlText = document.getElementById('oracleSqlExample').textContent;
                break;
            case 'mysql':
                sqlText = document.getElementById('mysqlSqlExample').textContent;
                break;
            case 'custom':
                sqlText = document.getElementById('customSqlExample').textContent;
                break;
            default:
                return;
        }

        // 清空表名和列名参数
        const tableNameInput = document.getElementById('tableNameInput');
        const creditCodeInput = document.getElementById('creditCodeColumnInput');
        const companyNameInput = document.getElementById('companyNameColumnInput');
        const businessKeyInput = document.getElementById('businessKeyColumnInput');
        const delMarkInput = document.getElementById('delMarkColumnInput');
        const subOrgInput = document.getElementById('subOrgColumnInput');
        const updateSqlTextarea = document.getElementById('updateSqlTextarea');

        if (tableNameInput) tableNameInput.value = '';
        if (creditCodeInput) creditCodeInput.value = '';
        if (companyNameInput) companyNameInput.value = '';
        if (businessKeyInput) businessKeyInput.value = '';
        if (delMarkInput) delMarkInput.value = '';
        if (subOrgInput) subOrgInput.value = '';

        // 清空更新SQL并填充到查询SQL输入框
        if (updateSqlTextarea) updateSqlTextarea.value = '';

        const sqlTextarea = document.getElementById('sqlTextarea');
        if (sqlTextarea) {
            sqlTextarea.value = sqlText.trim();

            // 重新识别表名和列名
            autoFillTableName();
            parseFieldsFromSql();
        }
        showMessage('查询SQL示例已选择，表名和列名已重新识别', 'success');

        // 保存配置
        saveConfig();

        // 关闭模态框
        closeSqlExamplesModal();
    }

    // 选择更新SQL示例
    function selectUpdateSqlExample(type) {
        let sqlText = '';

        switch (type) {
            case 'update-basic':
                sqlText = document.getElementById('updateBasicSqlExample').textContent;
                break;
            case 'update-advanced':
                sqlText = document.getElementById('updateAdvancedSqlExample').textContent;
                break;
            default:
                return;
        }

        // 清理SQL文本
        sqlText = sqlText.trim();

        // 填充到更新SQL文本框
        const updateSqlTextarea = document.getElementById('updateSqlTextarea');
        if (updateSqlTextarea) {
            updateSqlTextarea.value = sqlText;
            // 保存配置
            saveConfig();
            showMessage('更新SQL示例已选择', 'success');
        }

        // 关闭模态框
        closeUpdateSqlExamplesModal();
    }

    // 自动刷新功能
    function startAutoRefresh() {
        const interval = parseInt(document.getElementById('refreshInterval').value);
        const statusDiv = document.getElementById('refreshStatus');

        stopAutoRefresh(); // 先停止之前的定时器

        autoRefreshTimer = setInterval(() => {
            if (currentLogFileName) {
                analyzeLog();
                updateRefreshStatus();
            }
        }, interval);

        updateRefreshStatus();
    }

    function stopAutoRefresh() {
        if (autoRefreshTimer) {
            clearInterval(autoRefreshTimer);
            autoRefreshTimer = null;
        }
        document.getElementById('refreshStatus').textContent = '';
    }

    function updateRefreshStatus() {
        const statusDiv = document.getElementById('refreshStatus');
        const now = new Date();
        statusDiv.textContent = `最后刷新: ${now.toLocaleTimeString()}`;
    }

    // API地址验证
    function validateApiUrl(apiUrl) {
        if (!apiUrl || apiUrl.trim() === '') {
            return true; // 空值允许，使用默认配置
        }

        try {
            const url = new URL(apiUrl);

            // 检查协议
            if (url.protocol !== 'http:' && url.protocol !== 'https:') {
                showMessage('API地址必须使用HTTP或HTTPS协议', 'warning');
                return false;
            }

            // 检查主机名
            if (!url.hostname) {
                showMessage('API地址格式不正确，缺少主机名', 'warning');
                return false;
            }

            // 检查常见端点
            const path = url.pathname.toLowerCase();
            const commonEndpoints = [
                'chat/completions',
                'completions',
                'messages',
                'generate'
            ];

            const hasValidEndpoint = commonEndpoints.some(endpoint => path.includes(endpoint));

            if (!hasValidEndpoint) {
                showMessage('API地址可能不正确，请确认端点支持OpenAI格式', 'warning');
            }

            // 提供配置建议
            if (url.hostname.includes('openai.com')) {
                if (!path.includes('chat/completions')) {
                    showMessage('OpenAI API建议使用: https://api.openai.com/v1/chat/completions', 'info');
                }
            } else if (url.hostname.includes('anthropic.com')) {
                if (!path.includes('messages')) {
                    showMessage('Claude API建议使用: https://api.anthropic.com/v1/messages', 'info');
                }
            }

            return true;

        } catch (error) {
            showMessage('API地址格式不正确: ' + error.message, 'error');
            return false;
        }
    }

    // 渲染Markdown内容
    function renderMarkdown(markdownText) {
        if (!markdownText || !markdownText.trim()) {
            return '';
        }

        try {
            // 清理markdown文本，移除代码块标记
            let cleanText = markdownText.trim();

            // 移除开头和结尾的```markdown标记
            if (cleanText.startsWith('```markdown')) {
                cleanText = cleanText.substring(11); // 移除```markdown
            }
            if (cleanText.startsWith('```')) {
                cleanText = cleanText.substring(3); // 移除```
            }
            if (cleanText.endsWith('```')) {
                cleanText = cleanText.substring(0, cleanText.length - 3); // 移除结尾的```
            }

            cleanText = cleanText.trim();

            // 使用marked.js渲染markdown
            if (typeof marked !== 'undefined') {
                return marked.parse(cleanText);
            } else {
                // 如果marked.js未加载，返回原始文本
                console.warn('Marked.js not loaded, displaying raw text');
                return cleanText.replace(/\n/g, '<br>');
            }
        } catch (error) {
            console.error('Error rendering markdown:', error);
            return markdownText.replace(/\n/g, '<br>');
        }
    }

    // 将markdown转换为纯文本（用于tooltip）
    function markdownToPlainText(markdownText) {
        if (!markdownText || !markdownText.trim()) {
            return '';
        }

        // 清理markdown文本，移除代码块标记
        let cleanText = markdownText.trim();

        // 移除开头和结尾的```markdown标记
        if (cleanText.startsWith('```markdown')) {
            cleanText = cleanText.substring(11);
        }
        if (cleanText.startsWith('```')) {
            cleanText = cleanText.substring(3);
        }
        if (cleanText.endsWith('```')) {
            cleanText = cleanText.substring(0, cleanText.length - 3);
        }

        cleanText = cleanText.trim();

        // 移除markdown语法，保留纯文本
        return cleanText
            .replace(/```[\s\S]*?```/g, '') // 移除代码块
            .replace(/`([^`]+)`/g, '$1') // 移除行内代码
            .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体
            .replace(/~~([^~]+)~~/g, '$1') // 移除删除线
            .replace(/#{1,6}\s+/g, '') // 移除标题标记
            .replace(/[-*+]\s+/g, '• ') // 转换列表项
            .replace(/\n+/g, ' ') // 将换行转为空格
            .trim();
    }

    // 消息提示
    function showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        const messageDiv = document.createElement('div');

        const bgColor = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'warning': 'bg-yellow-500',
            'info': 'bg-blue-500'
        }[type] || 'bg-blue-500';

        messageDiv.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 fade-in`;
        messageDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

        container.appendChild(messageDiv);

        // 自动移除消息
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }

    // 提交检查表单
    document.getElementById('checkFormData').addEventListener('submit', async (e) => {
        e.preventDefault();

        // 保存配置到缓存
        saveConfig();

        const formData = new FormData(e.target);
        const params = new URLSearchParams();

        for (let [key, value] of formData.entries()) {
            params.append(key, value);
        }

        try {
            const response = await fetch('/api/crpt/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            });

            const result = await response.json();

            if (result.code === 200) {
                showMessage(result.message || '匹配检查任务已启动', 'success');
                hideCheckForm();

                // 自动跳转到日志分析页面
                setTimeout(() => {
                    showAnalysisPanel();
                    // 自动选择今天的日志文件
                    autoSelectTodayLogFile();
                }, 1000);
            } else {
                showMessage(result.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showMessage('网络错误，请稍后重试', 'error');
        }
    });

    // 加载日志文件列表
    async function loadLogFiles(autoSelect = false) {
        try {
            const response = await fetch('/api/log/files');
            const result = await response.json();

            if (result.code === 200) {
                const select = document.getElementById('logFileSelect');
                select.innerHTML = '<option value="">请选择日志文件</option>';

                result.data.forEach(file => {
                    const option = document.createElement('option');
                    option.value = file;
                    option.textContent = file;
                    select.appendChild(option);
                });

                // 如果需要自动选择且有文件，选择第一个文件
                if (autoSelect && result.data.length > 0) {
                    select.value = result.data[0];
                    currentLogFileName = result.data[0];
                    // 自动分析日志
                    setTimeout(() => {
                        analyzeLog();
                    }, 100);
                }
            } else {
                showMessage(result.message || '加载日志文件失败', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showMessage('加载日志文件失败', 'error');
        }
    }

    // 自动选择今天的日志文件
    async function autoSelectTodayLogFile() {
        await loadLogFiles(); // 先加载日志文件列表

        const select = document.getElementById('logFileSelect');
        const today = new Date().toISOString().split('T')[0]; // 格式: YYYY-MM-DD

        // 查找今天的日志文件
        for (let option of select.options) {
            if (option.value.includes(today)) {
                select.value = option.value;
                currentLogFileName = option.value;
                // 自动分析日志
                setTimeout(() => {
                    analyzeLog();
                }, 500);
                break;
            }
        }
    }

    // 分析日志
    async function analyzeLog() {
        const fileName = document.getElementById('logFileSelect').value;

        if (!fileName) {
            showMessage('请选择日志文件', 'warning');
            return;
        }

        // 防重复点击检查
        if (isAnalyzing) {
            showMessage('正在分析中，请稍候...', 'warning');
            return;
        }

        // 更新当前日志文件名
        currentLogFileName = fileName;

        // 获取分析按钮
        const analyzeBtn = document.querySelector('button[onclick="analyzeLog()"]');

        try {
            // 设置分析状态
            isAnalyzing = true;

            // 禁用分析按钮并显示加载状态
            if (analyzeBtn) {
                analyzeBtn.disabled = true;
                analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>分析中...';
            }

            // 只传递文件名，不传递筛选条件（在前端处理）
            const params = new URLSearchParams({fileName});

            const response = await fetch(`/api/log/analyze?${params}`);
            const result = await response.json();

            if (result.code === 200) {
                displayAnalysisResults(result.data);

                // 加载已选择的企业信息
                await loadSelectedCompanies(fileName);

                showMessage('日志分析完成', 'success');
            } else {
                showMessage(result.message || '分析失败', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showMessage('分析失败', 'error');
        } finally {
            // 恢复分析状态和按钮
            isAnalyzing = false;
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '<i class="fas fa-search mr-2"></i>分析';
            }
        }
    }

    // 显示分析结果
    function displayAnalysisResults(data) {
        // 显示配置信息
        if (data.configInfo) {
            displayConfigInfo(data.configInfo);
            document.getElementById('configInfoPanel').classList.remove('hidden');
        }

        // 显示统计信息
        displayStatistics(data.statistics, data.tagDescriptions);

        // 显示结果表格
        displayResultsTable(data.matchResults);

        // 显示面板
        document.getElementById('statisticsPanel').classList.remove('hidden');
        document.getElementById('resultsTable').classList.remove('hidden');
    }

    // 全局变量存储当前配置信息
    let currentConfigInfo = null;

    // 显示配置信息
    function displayConfigInfo(configInfo) {
        currentConfigInfo = configInfo;
        const container = document.getElementById('configInfoContent');

        if (!configInfo) {
            container.innerHTML = '<div class="text-blue-700 dark:text-blue-300">未找到配置信息</div>';
            return;
        }

        const formatValue = (value) => {
            if (value === null || value === undefined) return '-';
            if (typeof value === 'boolean') return value ? '是' : '否';
            if (typeof value === 'string' && value.length > 50) {
                return value.substring(0, 50) + '...';
            }
            return value;
        };

        container.innerHTML = `
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">记录时间:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.timestamp)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">数据库类型:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.databaseType)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">表名:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.tableName)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">使用AI:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.useAi)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">分页大小:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.pageSize)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">API间隔:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.apiInterval)}ms</div>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">信用代码列:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.creditCodeColumn)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">企业名称列:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.companyNameColumn)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">业务主键列:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.businessKeyColumn)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">删除标记列:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.delMarkColumn)}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">分支机构列:</div>
                        <div class="text-blue-800 dark:text-blue-200">${formatValue(configInfo.subOrgColumn)}</div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-24 text-blue-700 dark:text-blue-300 font-medium">查询SQL:</div>
                        <div class="text-blue-800 dark:text-blue-200 font-mono text-xs break-all">${formatValue(configInfo.querySql)}</div>
                    </div>
                </div>
            `;
    }

    // 编辑更新SQL
    function editUpdateSql() {
        if (!currentConfigInfo) {
            showMessage('未找到配置信息', 'error');
            return;
        }

        // 显示模态框
        document.getElementById('editUpdateSqlModal').classList.remove('hidden');
        document.body.classList.add('modal-open');

        // 填充当前配置信息
        displayCurrentConfigInfo();

        // 填充更新SQL
        const textarea = document.getElementById('editUpdateSqlTextarea');
        textarea.value = currentConfigInfo.updateSql || '';

        // 隐藏预览区域
        document.getElementById('sqlPreviewSection').classList.add('hidden');
    }

    // 显示当前配置信息
    function displayCurrentConfigInfo() {
        const container = document.getElementById('currentConfigInfo');

        if (!currentConfigInfo) {
            container.innerHTML = '<div class="text-blue-700 dark:text-blue-300">未找到配置信息</div>';
            return;
        }

        const formatValue = (value) => {
            if (value === null || value === undefined) return '-';
            if (typeof value === 'boolean') return value ? '是' : '否';
            return value;
        };

        container.innerHTML = `
                <div class="space-y-2">
                    <div><strong>数据库类型:</strong> ${formatValue(currentConfigInfo.databaseType)}</div>
                    <div><strong>表名:</strong> ${formatValue(currentConfigInfo.tableName)}</div>
                    <div><strong>信用代码列:</strong> ${formatValue(currentConfigInfo.creditCodeColumn)}</div>
                    <div><strong>企业名称列:</strong> ${formatValue(currentConfigInfo.companyNameColumn)}</div>
                </div>
                <div class="space-y-2">
                    <div><strong>业务主键列:</strong> ${formatValue(currentConfigInfo.businessKeyColumn)}</div>
                    <div><strong>删除标记列:</strong> ${formatValue(currentConfigInfo.delMarkColumn)}</div>
                    <div><strong>分支机构列:</strong> ${formatValue(currentConfigInfo.subOrgColumn)}</div>
                    <div><strong>使用AI:</strong> ${formatValue(currentConfigInfo.useAi)}</div>
                </div>
            `;
    }

    // 显示统计信息
    function displayStatistics(statistics, tagDescriptions) {
        const container = document.getElementById('statisticsCards');
        container.innerHTML = '';

        const colors = {
            1: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            2: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
            3: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
            4: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
            5: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
        };

        Object.entries(statistics).forEach(([tag, count]) => {
            const card = document.createElement('div');
            card.className = `${colors[tag] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'} rounded-lg p-4 text-center cursor-pointer hover:opacity-80 transition-opacity`;
            card.onclick = () => filterByMatchTag(tag);
            card.innerHTML = `
                    <div class="text-2xl font-bold">${count}</div>
                    <div class="text-sm">${tagDescriptions[tag] || '未知状态'}</div>
                    <div class="text-xs mt-1 opacity-75">点击筛选</div>
                `;
            container.appendChild(card);
        });
    }

    // 显示结果表格
    function displayResultsTable(matchResults) {
        // 保存当前匹配结果数据，供JSON查看功能使用
        currentMatchResults = matchResults;
        filteredResults = matchResults;
        totalRecords = matchResults.length;
        currentPage = 1;

        // 应用当前筛选条件
        applyFilters();
    }

    // 应用筛选条件
    function applyFilters() {
        const matchTag = document.getElementById('matchTagFilter').value;
        const processedStatus = document.getElementById('processedFilter').value;
        const keyword = document.getElementById('keywordFilter').value.toLowerCase();

        // 筛选数据
        filteredResults = currentMatchResults.filter(result => {
            const matchTagMatch = !matchTag || result.matchTag == matchTag;

            // 处理状态筛选
            let processedMatch = true;
            if (processedStatus === 'processed') {
                processedMatch = result.processed === true && result.matchTag !== 1;
            } else if (processedStatus === 'unprocessed') {
                processedMatch = result.processed !== true && result.matchTag !== 1;
            } else if (processedStatus === 'success') {
                processedMatch = result.matchTag === 1;
            }

            const keywordMatch = !keyword ||
                (result.originalCompanyName && result.originalCompanyName.toLowerCase().includes(keyword)) ||
                (result.originalCreditCode && result.originalCreditCode.toLowerCase().includes(keyword)) ||
                (result.businessKey && result.businessKey.toLowerCase().includes(keyword));

            return matchTagMatch && processedMatch && keywordMatch;
        });

        totalRecords = filteredResults.length;
        currentPage = 1;

        // 渲染表格
        renderTable();
    }

    // 渲染表格
    function renderTable() {
        const tbody = document.getElementById('resultsTableBody');
        const countSpan = document.getElementById('resultCount');

        tbody.innerHTML = '';
        countSpan.textContent = `共 ${totalRecords} 条记录`;

        // 计算分页
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, totalRecords);
        const pageData = filteredResults.slice(startIndex, endIndex);

        const tagDescriptions = {
            1: '全部匹配成功',
            2: '社会信用代码匹配成功',
            3: '单位名称匹配成功',
            4: '全部匹配失败',
            5: '不是同一个单位'
        };

        const tagColors = {
            1: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            2: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
            3: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
            4: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
            5: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
        };

        pageData.forEach((result, index) => {
            const globalIndex = startIndex + index; // 全局索引，用于JSON查看
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700';
            const isProcessed = result.processed || false;
            const isNonSuccessMatch = result.matchTag !== 1; // 非全部匹配成功的记录才显示处理按钮

            row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span class="copyable" onclick="copyToClipboard('${(result.businessKey || '').replace(/'/g, "\\'")}', this)" title="点击复制">
                            ${result.businessKey || '-'}
                            <span class="copy-tooltip">点击复制</span>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span class="copyable" onclick="copyToClipboard('${(result.originalCompanyName || '').replace(/'/g, "\\'")}', this)" title="点击复制">
                            ${result.originalCompanyName || '-'}
                            <span class="copy-tooltip">点击复制</span>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span class="copyable" onclick="copyToClipboard('${(result.originalCreditCode || '').replace(/'/g, "\\'")}', this)" title="点击复制">
                            ${result.originalCreditCode || '-'}
                            <span class="copy-tooltip">点击复制</span>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${tagColors[result.matchTag] || 'bg-gray-100 text-gray-800'}"
                              ${result.aiAnalysis ? `title="${markdownToPlainText(result.aiAnalysis).replace(/"/g, '&quot;')}"` : ''}>
                            ${result.matchTag} - ${tagDescriptions[result.matchTag] || '未知'}
                            ${result.aiAnalysis ? '<i class="fas fa-robot ml-1 text-xs"></i>' : ''}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${isNonSuccessMatch ? `
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isProcessed ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}">
                                <i class="fas ${isProcessed ? 'fa-check-circle' : 'fa-clock'} mr-1"></i>
                                ${isProcessed ? '已处理' : '待处理'}
                            </span>
                        ` : `
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                <i class="fas fa-check-double mr-1"></i>
                                匹配成功
                            </span>
                        `}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                        <button onclick="showJsonModal(${globalIndex})" class="bg-blue-500 hover:bg-blue-700 text-white font-medium py-1 px-3 rounded text-xs transition-colors">
                            <i class="fas fa-eye mr-1"></i>查看
                        </button>

                    </td>
                `;
            tbody.appendChild(row);
        });

        // 更新分页信息
        updatePaginationInfo();
    }

    // 更新分页信息
    function updatePaginationInfo() {
        const totalPages = Math.ceil(totalRecords / pageSize);
        const startIndex = (currentPage - 1) * pageSize + 1;
        const endIndex = Math.min(currentPage * pageSize, totalRecords);

        document.getElementById('pageStart').textContent = totalRecords > 0 ? startIndex : 0;
        document.getElementById('pageEnd').textContent = endIndex;
        document.getElementById('totalCount').textContent = totalRecords;
        document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;

        // 更新按钮状态
        const prevBtn = document.getElementById('prevPageBtn');
        const nextBtn = document.getElementById('nextPageBtn');

        prevBtn.disabled = currentPage <= 1;
        nextBtn.disabled = currentPage >= totalPages;

        // 显示/隐藏分页控件
        const paginationContainer = document.getElementById('paginationContainer');
        if (totalRecords > 0) {
            paginationContainer.classList.remove('hidden');
        } else {
            paginationContainer.classList.add('hidden');
        }
    }

    // 切换页面
    function changePage(direction) {
        const totalPages = Math.ceil(totalRecords / pageSize);
        const newPage = currentPage + direction;

        if (newPage >= 1 && newPage <= totalPages) {
            currentPage = newPage;
            renderTable();
        }
    }

    // 根据匹配标识筛选
    function filterByMatchTag(tag) {
        document.getElementById('matchTagFilter').value = tag;
        applyFilters();
    }

    // 切换处理状态
    async function toggleProcessedStatus(businessKey, markAsProcessed, globalIndex) {
        if (!currentLogFileName) {
            showMessage('请先选择日志文件', 'warning');
            return;
        }

        if (!businessKey) {
            showMessage('业务主键不能为空', 'error');
            return;
        }

        try {
            const action = markAsProcessed ? 'mark' : 'unmark';
            const params = new URLSearchParams({
                logFileName: currentLogFileName,
                businessKey: businessKey
            });

            const response = await fetch(`/api/processed/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            });

            const result = await response.json();

            if (result.code === 200) {
                // 更新本地数据
                if (globalIndex >= 0 && globalIndex < filteredResults.length) {
                    filteredResults[globalIndex].processed = markAsProcessed;

                    // 同时更新原始数据
                    const originalResult = currentMatchResults.find(r => r.businessKey === businessKey);
                    if (originalResult) {
                        originalResult.processed = markAsProcessed;
                    }
                }

                // 重新渲染表格
                renderTable();

                // 显示成功消息
                const actionText = markAsProcessed ? '标记为已处理' : '取消已处理标记';
                showMessage(`${actionText}成功`, 'success');
            } else {
                showMessage(result.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showMessage('网络错误，请稍后重试', 'error');
        }
    }

    // 清空关键词搜索
    function clearKeywordSearch() {
        const keywordFilter = document.getElementById('keywordFilter');
        const clearBtn = document.getElementById('clearKeywordBtn');

        keywordFilter.value = '';
        clearBtn.classList.add('hidden');

        // 如果有数据，重新应用筛选
        if (currentMatchResults.length > 0) {
            applyFilters();
        }
    }

    // 复制到剪贴板功能
    function copyToClipboard(text, element) {
        // 如果文本为空或只是占位符，不执行复制
        if (!text || text === '-' || text.trim() === '') {
            showMessage('无内容可复制', 'warning');
            return;
        }

        try {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopySuccess(element);
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyToClipboard(text, element);
                });
            } else {
                fallbackCopyToClipboard(text, element);
            }
        } catch (error) {
            console.error('复制失败:', error);
            showMessage('复制失败', 'error');
        }
    }

    // 备用复制方法
    function fallbackCopyToClipboard(text, element) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess(element);
            } else {
                showMessage('复制失败', 'error');
            }
        } catch (err) {
            console.error('备用复制方法失败:', err);
            showMessage('复制失败', 'error');
        } finally {
            document.body.removeChild(textArea);
        }
    }

    // 显示复制成功效果
    function showCopySuccess(element) {
        showMessage('已复制到剪贴板', 'success');

        // 添加复制成功的视觉效果
        if (element) {
            const originalBg = element.style.backgroundColor;
            element.style.backgroundColor = '#10B981';
            element.style.color = 'white';

            setTimeout(() => {
                element.style.backgroundColor = originalBg;
                element.style.color = '';
            }, 300);
        }
    }

    // 更新清空按钮的显示状态
    function updateClearButtonVisibility() {
        const keywordFilter = document.getElementById('keywordFilter');
        const clearBtn = document.getElementById('clearKeywordBtn');

        if (keywordFilter.value.trim()) {
            clearBtn.classList.remove('hidden');
        } else {
            clearBtn.classList.add('hidden');
        }
    }

    // 导出结果
    async function exportResults() {
        const fileName = document.getElementById('logFileSelect').value;
        const matchTag = document.getElementById('matchTagFilter').value;
        const keyword = document.getElementById('keywordFilter').value;

        if (!fileName) {
            showMessage('请选择日志文件', 'warning');
            return;
        }

        try {
            const params = new URLSearchParams({fileName});
            if (matchTag) params.append('matchTag', matchTag);
            if (keyword) params.append('keyword', keyword);

            const response = await fetch(`/api/log/export?${params}`);

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `export-${new Date().getTime()}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                showMessage('导出成功', 'success');
            } else {
                showMessage('导出失败', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showMessage('导出失败', 'error');
        }
    }

    // JSON模态框相关功能
    let currentJsonData = {creditCode: '', companyName: ''};
    let currentActiveTab = 'creditCode';

    // 显示JSON模态框
    function showJsonModal(index) {
        if (index >= 0 && index < filteredResults.length) {
            const result = filteredResults[index];

            // 设置模态框标题
            document.getElementById('jsonModalTitle').textContent = `${result.originalCompanyName || '未知企业'} - API返回数据详情`;

            // 显示原始查询信息
            const businessKeyEl = document.getElementById('modalBusinessKey');
            const companyNameEl = document.getElementById('modalCompanyName');
            const creditCodeEl = document.getElementById('modalCreditCode');

            businessKeyEl.innerHTML = `${result.businessKey || '-'}<span class="copy-tooltip">点击复制</span>`;
            companyNameEl.innerHTML = `${result.originalCompanyName || '-'}<span class="copy-tooltip">点击复制</span>`;
            creditCodeEl.innerHTML = `${result.originalCreditCode || '-'}<span class="copy-tooltip">点击复制</span>`;

            // 更新复制事件
            businessKeyEl.onclick = () => copyToClipboard(result.businessKey || '', businessKeyEl);
            companyNameEl.onclick = () => copyToClipboard(result.originalCompanyName || '', companyNameEl);
            creditCodeEl.onclick = () => copyToClipboard(result.originalCreditCode || '', creditCodeEl);

            // 显示AI分析结果
            const aiAnalysisSection = document.getElementById('aiAnalysisSection');
            const aiAnalysisContent = document.getElementById('aiAnalysisContent');
            if (result.aiAnalysis && result.aiAnalysis.trim()) {
                // 渲染markdown格式的AI分析结果
                aiAnalysisContent.innerHTML = renderMarkdown(result.aiAnalysis);
                aiAnalysisContent.className = 'text-sm text-blue-800 dark:text-blue-200 markdown-content';
                aiAnalysisSection.classList.remove('hidden');
            } else {
                aiAnalysisSection.classList.add('hidden');
            }

            // 显示处理状态按钮（仅对非全部匹配成功的记录）
            const modalProcessedActions = document.getElementById('modalProcessedActions');
            const isNonSuccessMatch = result.matchTag !== 1;
            const isProcessed = result.processed || false;


            // 准备JSON数据
            currentJsonData.creditCode = result.creditCodeMatchJson || '{}';
            currentJsonData.companyName = result.companyNameMatchJson || '{}';


            // 显示企业选择区域（仅对非全部匹配成功的记录）
            const companySelectionColumn = document.getElementById('companySelectionColumn');
            const modalContentGrid = document.getElementById('modalContentGrid');

            if (isNonSuccessMatch) {
                displayCompanySelectionInModal(result, index);
                companySelectionColumn.classList.remove('hidden');
                modalContentGrid.className = 'grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4 flex-1 min-h-0 h-full';
            } else {
                companySelectionColumn.classList.add('hidden');
                modalContentGrid.className = 'grid grid-cols-1 gap-6 mt-4 flex-1 min-h-0 h-full';
            }

            // 显示模态框并锁定背景滚动
            document.getElementById('jsonModal').classList.remove('hidden');
            document.body.classList.add('modal-open');
        }
    }

    // 关闭JSON模态框
    function closeJsonModal() {
        document.getElementById('jsonModal').classList.add('hidden');
        document.body.classList.remove('modal-open');
    }

    // 在模态框中切换处理状态
    async function toggleProcessedStatusInModal(businessKey, markAsProcessed, modalIndex) {
        try {
            // 调用处理状态切换函数
            await toggleProcessedStatus(businessKey, markAsProcessed, modalIndex);

            // 更新模态框中的按钮状态
            const result = filteredResults[modalIndex];
            const modalProcessedActions = document.getElementById('modalProcessedActions');
            const isProcessed = result.processed || false;

            modalProcessedActions.innerHTML = `
                    <button onclick="toggleProcessedStatusInModal('${businessKey}', ${!isProcessed}, ${modalIndex})"
                            class="${isProcessed ? 'bg-orange-500 hover:bg-orange-700' : 'bg-green-500 hover:bg-green-700'} text-white font-medium py-2 px-4 rounded transition-colors">
                        <i class="fas ${isProcessed ? 'fa-undo' : 'fa-check'} mr-2"></i>
                        ${isProcessed ? '取消已处理标记' : '标记为已处理'}
                    </button>
                `;
        } catch (error) {
            console.error('模态框中切换处理状态失败:', error);
        }
    }


    // 格式化JSON字符串
    function formatJson(jsonString) {
        if (!jsonString || jsonString.trim() === '') {
            return '暂无数据';
        }

        try {
            const parsed = JSON.parse(jsonString);
            return JSON.stringify(parsed, null, 2);
        } catch (error) {
            return jsonString; // 如果解析失败，返回原始字符串
        }
    }


    // 复制当前JSON到剪贴板
    function copyCurrentJsonToClipboard() {
        try {
            // 检查是否在查询结果详情模态框中
            const isInQueryResultsModal = !document.getElementById('queryResultsModal').classList.contains('hidden');
            const activeTab = isInQueryResultsModal ? currentModalActiveTab : currentActiveTab;

            const activeContent = activeTab === 'creditCode'
                ? currentJsonData.creditCode
                : currentJsonData.companyName;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(activeContent).then(() => {
                    showMessage('JSON已复制到剪贴板', 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyToClipboard(activeContent);
                });
            } else {
                fallbackCopyToClipboard(activeContent);
            }
        } catch (error) {
            console.error('复制JSON失败:', error);
            showMessage('复制失败', 'error');
        }
    }

    // 备用复制方法
    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            showMessage('JSON已复制到剪贴板', 'success');
        } catch (err) {
            console.error('备用复制方法失败:', err);
            showMessage('复制失败，请手动复制', 'error');
        }
        document.body.removeChild(textArea);
    }

    // 页面加载完成后初始化事件监听器
    document.addEventListener('DOMContentLoaded', function () {
        // SQL输入框变化时自动填充表名和解析字段
        const sqlTextarea = document.getElementById('sqlTextarea');
        if (sqlTextarea) {
            sqlTextarea.addEventListener('input', () => {
                autoFillTableName();
                parseFieldsFromSql();
            });
            sqlTextarea.addEventListener('paste', () => {
                setTimeout(() => {
                    autoFillTableName();
                    parseFieldsFromSql();
                }, 100); // 延迟执行，确保粘贴内容已处理
            });
        }

        // 自动刷新复选框事件
        const autoRefreshCheckbox = document.getElementById('autoRefreshCheckbox');
        const refreshInterval = document.getElementById('refreshInterval');

        if (autoRefreshCheckbox) {
            autoRefreshCheckbox.addEventListener('change', function () {
                if (this.checked) {
                    refreshInterval.disabled = false;
                    startAutoRefresh();
                } else {
                    refreshInterval.disabled = true;
                    stopAutoRefresh();
                }
            });
        }

        // 刷新间隔变化时重新启动自动刷新
        if (refreshInterval) {
            refreshInterval.addEventListener('change', function () {
                if (autoRefreshCheckbox.checked) {
                    startAutoRefresh();
                }
            });
        }

        // AI分析选项控制
        const useAiCheckbox = document.getElementById('useAi');
        if (useAiCheckbox) {
            useAiCheckbox.addEventListener('change', function () {
                // 自动保存配置
                saveConfig();
            });
        }

        // 自动保存配置功能
        function setupAutoSave() {
            const configInputs = [
                'tableNameInput',
                'sqlTextarea',
                'creditCodeColumnInput',
                'companyNameColumnInput',
                'businessKeyColumnInput',
                'delMarkColumnInput',
                'subOrgColumnInput',
                'updateSqlTextarea'
            ];



            configInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    // 添加防抖延迟保存
                    let saveTimeout;
                    element.addEventListener('input', function () {
                        clearTimeout(saveTimeout);
                        saveTimeout = setTimeout(() => {
                            saveConfig();
                        }, 1000); // 1秒延迟保存
                    });

                    // 失去焦点时立即保存
                    element.addEventListener('blur', function () {
                        clearTimeout(saveTimeout);
                        saveConfig();
                    });
                }
            });

            // 为数据库类型单选按钮添加事件监听器
            const dbRadios = document.querySelectorAll('input[name="db"]');
            dbRadios.forEach(radio => {
                radio.addEventListener('change', function () {
                    saveConfig();
                });
            });


        }

        // 初始化自动保存
        setupAutoSave();

        // 页面加载时尝试恢复配置
        document.addEventListener('DOMContentLoaded', function () {
            // 延迟加载配置，确保所有元素都已渲染
            setTimeout(() => {
                const loaded = loadConfig();
                if (loaded) {
                    console.log('已恢复上次的配置');
                }
            }, 100);
        });

        // 日志文件选择变化时更新当前文件名
        const logFileSelect = document.getElementById('logFileSelect');
        if (logFileSelect) {
            logFileSelect.addEventListener('change', function () {
                currentLogFileName = this.value;
                if (this.value) {
                    analyzeLog();
                }
            });

            // 下拉框获得焦点时自动获取列表
            logFileSelect.addEventListener('focus', function () {
                loadLogFiles();
            });
        }

        // 匹配标识筛选变化时自动应用筛选
        const matchTagFilter = document.getElementById('matchTagFilter');
        if (matchTagFilter) {
            matchTagFilter.addEventListener('change', function () {
                if (currentMatchResults.length > 0) {
                    applyFilters();
                }
            });
        }

        // 处理状态筛选变化时自动应用筛选
        const processedFilter = document.getElementById('processedFilter');
        if (processedFilter) {
            processedFilter.addEventListener('change', function () {
                if (currentMatchResults.length > 0) {
                    applyFilters();
                }
            });
        }

        // 关键词搜索失焦自动搜索
        const keywordFilter = document.getElementById('keywordFilter');
        if (keywordFilter) {
            // 输入时更新清空按钮显示状态
            keywordFilter.addEventListener('input', function () {
                updateClearButtonVisibility();
            });

            keywordFilter.addEventListener('blur', function () {
                if (currentMatchResults.length > 0) {
                    applyFilters();
                }
            });

            // 回车键也触发搜索
            keywordFilter.addEventListener('keypress', function (e) {
                if (e.key === 'Enter' && currentMatchResults.length > 0) {
                    applyFilters();
                }
            });
        }


    });

    // 查询结果详情模态框相关功能
    let currentModalActiveTab = 'creditCode';

    // 显示查询结果详情模态框
    function showQueryResultsModal() {
        // 复制数据到模态框
        displayParsedApiInfoModal('creditCode', currentJsonData.creditCode);
        displayParsedApiInfoModal('companyName', currentJsonData.companyName);
        displayJsonContentModal();

        // 默认显示社会信用代码标签页
        switchJsonTabModal('creditCode');

        // 显示模态框并锁定背景滚动
        document.getElementById('queryResultsModal').classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 关闭查询结果详情模态框
    function closeQueryResultsModal() {
        document.getElementById('queryResultsModal').classList.add('hidden');
        //document.body.classList.remove('modal-open');
    }

    // 切换查询结果详情模态框的标签页
    function switchJsonTabModal(type) {
        currentModalActiveTab = type;

        // 更新标签页样式
        const creditCodeTab = document.getElementById('creditCodeTabModal');
        const companyNameTab = document.getElementById('companyNameTabModal');
        const creditCodeContent = document.getElementById('creditCodeContentModal');
        const companyNameContent = document.getElementById('companyNameContentModal');

        if (type === 'creditCode') {
            creditCodeTab.className = 'px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b-2 border-primary-500 text-primary-600 dark:text-primary-400';
            companyNameTab.className = 'px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-700 dark:hover:text-gray-300';
            creditCodeContent.classList.remove('hidden');
            companyNameContent.classList.add('hidden');
        } else {
            companyNameTab.className = 'px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b-2 border-primary-500 text-primary-600 dark:text-primary-400';
            creditCodeTab.className = 'px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-700 dark:hover:text-gray-300';
            companyNameContent.classList.remove('hidden');
            creditCodeContent.classList.add('hidden');
        }
    }

    // 显示JSON内容到模态框
    function displayJsonContentModal() {
        try {
            // 格式化并显示社会信用代码查询结果
            const creditCodeFormatted = formatJson(currentJsonData.creditCode);
            document.getElementById('creditCodeJsonContentModal').textContent = creditCodeFormatted;

            // 格式化并显示单位名称查询结果
            const companyNameFormatted = formatJson(currentJsonData.companyName);
            document.getElementById('companyNameJsonContentModal').textContent = companyNameFormatted;

        } catch (error) {
            console.error('显示JSON内容失败:', error);
            document.getElementById('creditCodeJsonContentModal').textContent = '解析JSON失败: ' + error.message;
            document.getElementById('companyNameJsonContentModal').textContent = '解析JSON失败: ' + error.message;
        }
    }

    // 显示解析后的API信息到模态框
    function displayParsedApiInfoModal(type, jsonString) {
        const containerId = type === 'creditCode' ? 'creditCodeParsedInfoModal' : 'companyNameParsedInfoModal';
        const container = document.getElementById(containerId);

        if (!jsonString || jsonString.trim() === '' || jsonString === '{}') {
            container.innerHTML = '<div class="text-sm text-gray-500 dark:text-gray-400">暂无查询数据</div>';
            return;
        }

        try {
            const apiData = JSON.parse(jsonString);
            let html = '';

            // 显示API调用状态
            if (apiData.error_code !== undefined) {
                const isSuccess = apiData.error_code === 0 || apiData.error_code === '0';
                html += `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">API状态:</span>
                            <span class="text-sm ${isSuccess ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'} ml-2">
                                ${isSuccess ? '成功' : '失败'} (${apiData.error_code})
                            </span>
                        </div>
                    `;

                if (!isSuccess && apiData.reason) {
                    html += `
                            <div class="mb-3">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">失败原因:</span>
                                <span class="text-sm text-red-600 dark:text-red-400 ml-2">${apiData.reason}</span>
                            </div>
                        `;
                }
            }

            // 显示企业列表
            if (apiData.result && apiData.result.items && Array.isArray(apiData.result.items)) {
                const items = apiData.result.items;
                html += `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">查询结果:</span>
                            <span class="text-sm text-blue-600 dark:text-blue-400 ml-2">共找到 ${items.length} 家企业</span>
                        </div>
                    `;

                if (items.length > 0) {
                    html += '<div class="space-y-2">';
                    // 显示所有企业，只显示企业名称和社会信用代码
                    items.forEach((item, index) => {
                        const companyName = item.name || '-';
                        const creditCode = item.creditCode || '-';
                        const companyType = item.companyType || '';
                        const base = item.base || '';
                        const legalPersonName = item.legalPersonName || '';
                        const regStatus = item.regStatus || '';

                        html += `
                                <div class="bg-white dark:bg-gray-600 p-3 rounded border border-gray-200 dark:border-gray-500">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm mb-2">
                                        <div>
                                            <span class="font-medium text-gray-700 dark:text-gray-300">企业名称:</span>
                                            <span class="copyable text-gray-900 dark:text-gray-100 ml-1" onclick="copyToClipboard('${companyName.replace(/'/g, "\\'")}', this)" title="点击复制">
                                                ${companyName}
                                                <span class="copy-tooltip">点击复制</span>
                                            </span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700 dark:text-gray-300">社会信用代码:</span>
                                            <span class="copyable text-gray-900 dark:text-gray-100 ml-1" onclick="copyToClipboard('${creditCode.replace(/'/g, "\\'")}', this)" title="点击复制">
                                                ${creditCode}
                                                <span class="copy-tooltip">点击复制</span>
                                            </span>
                                        </div>
                                        ${companyType ? `
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">企业类型:</span>
                                                <span class="text-gray-900 dark:text-gray-100 ml-1">${getCompanyTypeDisplay(companyType)}</span>
                                            </div>
                                        ` : ''}
                                        ${legalPersonName ? `
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">法定代表人:</span>
                                                <span class="text-gray-900 dark:text-gray-100 ml-1">${legalPersonName}</span>
                                            </div>
                                        ` : ''}
                                        ${regStatus ? `
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">企业状态:</span>
                                                <span class="text-gray-900 dark:text-gray-100 ml-1">${regStatus}</span>
                                            </div>
                                        ` : ''}
                                    </div>
                                    ${base ? `
                                        <div class="text-sm">
                                            <span class="font-medium text-gray-700 dark:text-gray-300">注册地址:</span>
                                            <span class="text-gray-900 dark:text-gray-100 ml-1">${base}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            `;
                    });
                    html += '</div>';
                }
            } else {
                html += '<div class="text-sm text-gray-500 dark:text-gray-400">未找到企业信息</div>';
            }

            container.innerHTML = html;
        } catch (error) {
            console.error('解析API数据失败:', error);
            container.innerHTML = `<div class="text-sm text-red-500 dark:text-red-400">解析API数据失败: ${error.message}</div>`;
        }
    }

    // 企业选择状态管理
    let selectedCompanyData = {}; // 存储每个记录的选择状态 {businessKey: selectedCompany}
    let aiRecommendedCompanies = {}; // 存储AI推荐的企业 {businessKey: [companies]}

    // 加载已选择的企业信息
    async function loadSelectedCompanies(logFileName) {
        try {
            if (!logFileName) {
                console.warn('日志文件名为空，无法加载选择信息');
                return;
            }

            const response = await fetch(`/api/log/getSelectedCompanies?logFileName=${encodeURIComponent(logFileName)}`);
            if (response.ok) {
                const result = await response.json();
                if (result.code === 200 && result.data) {
                    // 将后端数据转换为前端格式
                    selectedCompanyData = {};
                    Object.keys(result.data).forEach(businessKey => {
                        const selectionInfo = result.data[businessKey];
                        selectedCompanyData[businessKey] = {
                            name: selectionInfo.selectedCompanyName,
                            creditCode: selectionInfo.selectedCreditCode,
                            companyType: selectionInfo.selectedCompanyType,
                            base: selectionInfo.selectedBase,
                            legalPersonName: selectionInfo.selectedLegalPersonName,
                            regStatus: selectionInfo.selectedRegStatus,
                            source: selectionInfo.jsonSource,
                            jsonIndex: selectionInfo.jsonIndex
                        };
                    });
                    console.log('已加载选择信息:', selectedCompanyData);
                }
            } else {
                console.warn('加载选择信息失败:', response.statusText);
            }
        } catch (error) {
            console.error('加载选择信息时发生错误:', error);
        }
    }

    // 在模态框中显示企业选择功能
    function displayCompanySelectionInModal(result, resultIndex) {
        const companyList = document.getElementById('modalCompanySelectionList');
        companyList.innerHTML = '';

        const creditCodeJson = result.creditCodeMatchJson || '{}';
        const companyNameJson = result.companyNameMatchJson || '{}';
        let companies = extractCompaniesFromJson(creditCodeJson, companyNameJson);

        if (companies.length === 0) {
            companyList.innerHTML = `
                    <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-exclamation-circle text-2xl mb-2"></i>
                        <p>未找到可选择的企业信息</p>
                        <p class="text-sm mt-1">API返回的数据中没有有效的企业列表</p>
                    </div>
                `;
            return;
        }

        // 标记AI推荐的企业（根据实际AI分析结果来设置）
        const businessKey = result.businessKey;
        if (!aiRecommendedCompanies[businessKey]) {
            if (result.aiAnalysis && result.aiAnalysis.trim()) {
                // 从AI分析结果中提取推荐企业
                const aiRecommended = extractRecommendedCompaniesFromAI(result.aiAnalysis, companies);
                aiRecommendedCompanies[businessKey] = aiRecommended;
            }
        }

        // 获取已选择的企业
        const selectedCompany = selectedCompanyData[businessKey];
        const aiRecommended = aiRecommendedCompanies[businessKey] || [];

        // 对企业进行排序：已选择的置顶，然后是AI推荐的，最后是其他
        companies.sort((a, b) => {
            // 使用精确的JSON位置匹配
            const aIsSelected = selectedCompany &&
                selectedCompany.source === a.source &&
                selectedCompany.jsonIndex === a.jsonIndex;
            const bIsSelected = selectedCompany &&
                selectedCompany.source === b.source &&
                selectedCompany.jsonIndex === b.jsonIndex;

            // 已选择的置顶
            if (aIsSelected && !bIsSelected) return -1;
            if (!aIsSelected && bIsSelected) return 1;

            // AI推荐的其次
            const aKey = `${a.name}_${a.creditCode}`;
            const bKey = `${b.name}_${b.creditCode}`;
            const aIsRecommended = aiRecommended.some(rec => `${rec.name}_${rec.creditCode}` === aKey);
            const bIsRecommended = aiRecommended.some(rec => `${rec.name}_${rec.creditCode}` === bKey);

            if (aIsRecommended && !bIsRecommended) return -1;
            if (!aIsRecommended && bIsRecommended) return 1;

            return 0;
        });

        // 渲染企业列表
        companies.forEach((company, index) => {
            const companyKey = `${company.name}_${company.creditCode}`;
            // 使用精确的JSON位置匹配判断是否已选择
            const isSelected = selectedCompany &&
                selectedCompany.source === company.source &&
                selectedCompany.jsonIndex === company.jsonIndex;
            const isAiRecommended = aiRecommended.some(rec => `${rec.name}_${rec.creditCode}` === companyKey);
            const canSelect = !selectedCompany; // 只有未选择时才能选择

            const companyCard = document.createElement('div');
            companyCard.className = `border rounded-lg p-3 transition-colors ${
                isSelected
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-800/30 cursor-pointer'
                    : canSelect
                        ? 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                        : 'border-gray-200 dark:border-gray-600 opacity-50 cursor-not-allowed'
            }`;

            if (canSelect && !isSelected) {
                companyCard.onclick = () => selectCompanyInModal(company, result, resultIndex);
            } else if (isSelected) {
                companyCard.onclick = () => unselectCompanyInModal(company, result, resultIndex);
            }

            const companyTypeDisplay = getCompanyTypeDisplay(company.companyType);
            const companyStatusDisplay = getCompanyStatusDisplay(company.regStatus);

            companyCard.innerHTML = `
                    <div class="space-y-2">
                        <div>
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${company.name || '-'}</div>
                            <div class="text-xs text-gray-600 dark:text-gray-400">${company.creditCode || '-'}</div>
                        </div>
                        <div class="flex flex-wrap gap-1">
                            <span class="inline-block px-2 py-1 text-xs rounded-full ${
                company.source === 'creditCode'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
            }">
                                <i class="fas fa-search mr-1"></i>
                                ${company.source === 'creditCode' ? '根据代码查询' : '根据名称查询'}
                            </span>
                            ${companyTypeDisplay ? `
                                <span class="inline-block px-2 py-1 text-xs rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                                    <i class="fas fa-building mr-1"></i>${companyTypeDisplay}
                                </span>
                            ` : ''}
                            ${companyStatusDisplay ? `
                                <span class="inline-block px-2 py-1 text-xs rounded-full ${
                companyStatusDisplay === '存续' || companyStatusDisplay === '在业'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : companyStatusDisplay === '注销' || companyStatusDisplay === '吊销'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
            }">
                                    <i class="fas fa-info-circle mr-1"></i>${companyStatusDisplay}
                                </span>
                            ` : ''}
                            ${isAiRecommended ? `
                                <span class="inline-block px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    <i class="fas fa-star mr-1"></i>推荐
                                </span>
                            ` : ''}
                            ${isSelected ? `
                                <span class="inline-block px-2 py-1 text-xs rounded-full bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200">
                                    <i class="fas fa-check mr-1"></i>已选择
                                </span>
                                <span class="inline-block px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                    <i class="fas fa-undo mr-1"></i>点击撤销
                                </span>
                            ` : ''}
                        </div>
                    </div>
                `;

            companyList.appendChild(companyCard);
        });
    }

    /**
     * 从AI分析结果中提取推荐企业
     * @param {string} aiAnalysis - AI分析结果（Markdown格式）
     * @param {Array} companies - 可选企业列表
     * @returns {Array} 推荐的企业列表
     */
    function extractRecommendedCompaniesFromAI(aiAnalysis, companies) {
        if (!aiAnalysis || !companies || companies.length === 0) {
            return [];
        }

        try {
            // 从AI分析结果中提取企业名称和社会信用代码
            const extractedInfo = parseAIAnalysisForCompanyInfo(aiAnalysis);

            if (!extractedInfo.companyName && !extractedInfo.creditCode) {
                // 如果AI分析中没有找到企业信息，返回空数组
                return [];
            }

            // 在企业列表中查找匹配的企业
            const recommendedCompanies = [];

            companies.forEach(company => {
                let isRecommended = false;

                // 优先按社会信用代码匹配
                if (extractedInfo.creditCode && company.creditCode) {
                    if (company.creditCode === extractedInfo.creditCode) {
                        isRecommended = true;
                    }
                }

                // 如果社会信用代码不匹配，尝试企业名称匹配
                if (!isRecommended && extractedInfo.companyName && company.name) {
                    if (company.name === extractedInfo.companyName) {
                        isRecommended = true;
                    } else {
                        // 计算名称相似度，如果相似度很高也认为是推荐
                        const similarity = calculateSimilarity(extractedInfo.companyName, company.name);
                        if (similarity > 0.8) { // 相似度阈值
                            isRecommended = true;
                        }
                    }
                }

                if (isRecommended) {
                    recommendedCompanies.push(company);
                }
            });

            return recommendedCompanies;

        } catch (error) {
            console.warn('解析AI分析结果失败:', error);
            return [];
        }
    }

    /**
     * 解析AI分析结果中的企业信息
     * @param {string} aiAnalysis - AI分析结果
     * @returns {Object} 包含企业名称和社会信用代码的对象
     */
    function parseAIAnalysisForCompanyInfo(aiAnalysis) {
        const result = {
            companyName: null,
            creditCode: null
        };

        try {
            // 匹配企业名称的正则表达式
            // 匹配格式：- **企业名称**：[企业名称内容]
            const nameRegex = /[-*]\s*\*\*企业名称\*\*[：:]\s*([^\n\r]+)/i;
            const nameMatch = aiAnalysis.match(nameRegex);
            if (nameMatch && nameMatch[1]) {
                result.companyName = nameMatch[1].trim();
            }

            // 匹配社会信用代码的正则表达式
            // 匹配格式：- **社会信用代码**：[代码内容]
            const creditCodeRegex = /[-*]\s*\*\*社会信用代码\*\*[：:]\s*([^\n\r]+)/i;
            const creditCodeMatch = aiAnalysis.match(creditCodeRegex);
            if (creditCodeMatch && creditCodeMatch[1]) {
                result.creditCode = creditCodeMatch[1].trim();
            }

            console.log('从AI分析中提取的企业信息:', result);
            return result;

        } catch (error) {
            console.warn('解析AI分析企业信息失败:', error);
            return result;
        }
    }

    // 简单的字符串相似度计算
    function calculateSimilarity(str1, str2) {
        if (!str1 || !str2) return 0;
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        const editDistance = getEditDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    // 计算编辑距离
    function getEditDistance(str1, str2) {
        const matrix = [];
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        return matrix[str2.length][str1.length];
    }

    // 企业选择确认相关变量
    let pendingCompanySelection = null;

    // 防重复点击标志
    let isConfirmingSelection = false;
    let isConfirmingUnselection = false;

    // 在模态框中选择企业
    function selectCompanyInModal(company, result, resultIndex) {
        // 保存待选择的企业信息
        pendingCompanySelection = {
            company: company,
            result: result,
            resultIndex: resultIndex
        };

        // 显示企业信息到确认模态框
        displayCompanyConfirmInfo(company);

        // 显示确认模态框
        document.getElementById('companyConfirmModal').classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 显示企业确认信息
    function displayCompanyConfirmInfo(company) {
        if (!pendingCompanySelection) return;

        const {result} = pendingCompanySelection;

        // 显示选择的企业信息
        const companyContainer = document.getElementById('confirmCompanyInfo');
        companyContainer.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                        <label class="block text-xs font-medium text-green-700 dark:text-green-300 mb-1">企业名称</label>
                        <div class="text-sm text-gray-900 dark:text-gray-100 py-1">
                            ${company.name || '-'}
                        </div>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-green-700 dark:text-green-300 mb-1">社会信用代码</label>
                        <div class="text-sm text-gray-900 dark:text-gray-100 py-1">
                            ${company.creditCode || '-'}
                        </div>
                    </div>
                    ${company.companyType ? `
                        <div>
                            <label class="block text-xs font-medium text-green-700 dark:text-green-300 mb-1">企业类型</label>
                            <div class="text-sm text-gray-900 dark:text-gray-100 py-1">
                                ${getCompanyTypeDisplay(company.companyType)}
                            </div>
                        </div>
                    ` : ''}
                    ${company.legalPersonName ? `
                        <div>
                            <label class="block text-xs font-medium text-green-700 dark:text-green-300 mb-1">法定代表人</label>
                            <div class="text-sm text-gray-900 dark:text-gray-100 py-1">
                                ${company.legalPersonName}
                            </div>
                        </div>
                    ` : ''}
                    ${company.regStatus ? `
                        <div>
                            <label class="block text-xs font-medium text-green-700 dark:text-green-300 mb-1">企业状态</label>
                            <div class="text-sm text-gray-900 dark:text-gray-100 py-1">
                                ${company.regStatus}
                            </div>
                        </div>
                    ` : ''}
                    ${company.base ? `
                        <div>
                            <label class="block text-xs font-medium text-green-700 dark:text-green-300 mb-1">注册地址</label>
                            <div class="text-sm text-gray-900 dark:text-gray-100 py-1">
                                ${company.base}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;

        // 检查并显示变更信息
        displayChangesHighlight(result, company);

        // 显示更新SQL
        displayUpdateSql(result, company);
    }

    // 显示变更高亮信息
    function displayChangesHighlight(originalData, selectedCompany) {
        const changesContainer = document.getElementById('changesHighlight');
        const changesList = document.getElementById('changesList');
        const changes = [];

        // 检查企业名称变更
        const originalName = originalData.originalCompanyName || '';
        const selectedName = selectedCompany.name || '';
        if (originalName && selectedName && originalName !== selectedName) {
            changes.push({
                field: '企业名称',
                original: originalName,
                selected: selectedName,
                type: 'name'
            });
        }

        // 检查社会信用代码变更
        const originalCode = originalData.originalCreditCode || '';
        const selectedCode = selectedCompany.creditCode || '';
        if (originalCode && selectedCode && originalCode !== selectedCode) {
            changes.push({
                field: '社会信用代码',
                original: originalCode,
                selected: selectedCode,
                type: 'code'
            });
        }

        if (changes.length > 0) {
            let changesHtml = '';
            changes.forEach(change => {
                changesHtml += `
                        <div class="flex items-start space-x-2 text-sm">
                            <i class="fas fa-arrow-right text-yellow-600 dark:text-yellow-400 mt-0.5"></i>
                            <div class="flex-1">
                                <span class="font-medium text-yellow-800 dark:text-yellow-200">${change.field}变更：</span>
                                <div class="mt-1 space-y-1">
                                    <div class="text-red-600 dark:text-red-400">
                                        <span class="text-xs">原：</span>${change.original}
                                    </div>
                                    <div class="text-green-600 dark:text-green-400">
                                        <span class="text-xs">新：</span>${change.selected}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
            });

            changesList.innerHTML = changesHtml;
            changesContainer.classList.remove('hidden');
        } else {
            changesContainer.classList.add('hidden');
        }
    }

    // 显示更新SQL - 从当前配置信息中实时获取SQL模板
    function displayUpdateSql(result, company) {
        const updateSqlContainer = document.getElementById('updateSqlContent');

        // 优先使用当前配置信息中的updateSql，如果没有则使用结果中的updateSql
        let updateSqlTemplate = null;

        if (currentConfigInfo && currentConfigInfo.updateSql) {
            updateSqlTemplate = currentConfigInfo.updateSql;
            console.log('使用当前配置信息中的更新SQL模板:', updateSqlTemplate);
        } else if (result.updateSql) {
            updateSqlTemplate = result.updateSql;
            console.log('使用日志记录中的更新SQL模板:', updateSqlTemplate);
        }

        if (!updateSqlTemplate) {
            updateSqlContainer.innerHTML = `
                    <div class="text-gray-500 dark:text-gray-400 italic text-center py-2">
                        <i class="fas fa-info-circle mr-2"></i>未配置更新SQL语句
                    </div>
                `;
            return;
        }

        // 准备占位符数据（显示时使用选择的企业信息）
        const placeholderData = {
            // 新的语义化占位符
            newCreditCode: company.creditCode || '',
            newCompanyName: company.name || '',
            oldCreditCode: result.originalCreditCode || '',
            oldCompanyName: result.originalCompanyName || '',
            businessKey: result.businessKey || '',
            tableName: result.parsedTableName || currentConfigInfo?.tableName || '',
            businessKeyColumn: result.parsedBusinessKeyColumn || currentConfigInfo?.businessKeyColumn || '',

            // 向后兼容性 - 支持旧的占位符格式
            creditCode: company.creditCode || '',
            companyName: company.name || ''
        };

        // 生成实际的更新SQL（替换占位符，使用选择的企业信息）
        const actualUpdateSql = replacePlaceholders(updateSqlTemplate, placeholderData);
        console.log('原始更新SQL模板:', updateSqlTemplate);
        console.log('生成的实际更新SQL:', actualUpdateSql);

        // 显示SQL
        updateSqlContainer.innerHTML = `<pre class="whitespace-pre-wrap">${escapeHtml(actualUpdateSql)}</pre>`;

        // 保存SQL到全局变量以便复制
        window.currentUpdateSql = actualUpdateSql;
    }

    // 复制更新SQL
    function copyUpdateSql() {
        if (window.currentUpdateSql) {
            navigator.clipboard.writeText(window.currentUpdateSql).then(() => {
                showMessage('更新SQL已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                showMessage('复制失败，请手动复制', 'error');
            });
        }
    }

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 确认企业选择
    async function confirmCompanySelection() {
        if (!pendingCompanySelection) return;

        // 防重复点击
        if (isConfirmingSelection) {
            showMessage('正在处理中，请稍候...', 'warning');
            return;
        }

        const {company, result, resultIndex} = pendingCompanySelection;
        const businessKey = result.businessKey;

        try {
            // 设置处理中标志
            isConfirmingSelection = true;

            // 禁用确认按钮并显示加载状态
            const confirmBtn = document.querySelector('#companyConfirmModal button[onclick="confirmCompanySelection()"]');
            if (confirmBtn) {
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';
            }

            // 记录选择的企业
            selectedCompanyData[businessKey] = company;

            // 保存选择的企业信息到后端
            await saveSelectedCompanyInfo(businessKey, company);

            // 执行更新SQL语句
            await executeUpdateSql(result, company);

            // 执行标记已处理操作
            toggleProcessedStatus(businessKey, true, resultIndex);

            // 重新渲染企业选择列表以显示选择状态
            displayCompanySelectionInModal(result, resultIndex);

            // 关闭确认模态框
            closeCompanyConfirmModal();

            // 显示成功消息
            showMessage(`已选择企业"${company.name}"并标记为已处理，数据库已更新`, 'success');

            // 清空待选择数据
            pendingCompanySelection = null;

        } catch (error) {
            console.error('确认企业选择失败:', error);
            showMessage('确认选择失败: ' + error.message, 'error');
        } finally {
            // 重置处理中标志和按钮状态
            isConfirmingSelection = false;
            const confirmBtn = document.querySelector('#companyConfirmModal button[onclick="confirmCompanySelection()"]');
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="fas fa-check mr-2"></i>确认选择';
            }
        }
    }

    // 执行更新SQL语句 - 从当前配置信息中实时获取SQL模板
    async function executeUpdateSql(resultData, company) {
        try {
            // 从日志文件名中提取数据库类型
            const dbType = extractDbTypeFromLogFileName(currentLogFileName);
            if (!dbType) {
                throw new Error('无法从日志文件名中提取数据库类型');
            }

            // 优先使用当前配置信息中的updateSql，如果没有则使用结果中的updateSql
            let updateSqlTemplate = null;

            if (currentConfigInfo && currentConfigInfo.updateSql) {
                updateSqlTemplate = currentConfigInfo.updateSql;
                console.log('执行时使用当前配置信息中的更新SQL模板:', updateSqlTemplate);
            } else if (resultData.updateSql) {
                updateSqlTemplate = resultData.updateSql;
                console.log('执行时使用日志记录中的更新SQL模板:', updateSqlTemplate);
            }

            if (!updateSqlTemplate) {
                throw new Error('未找到更新SQL模板');
            }

            // 准备占位符数据（选择企业时使用新数据）
            const placeholderData = {
                // 新的语义化占位符
                newCreditCode: company.creditCode || '',
                newCompanyName: company.name || '',
                oldCreditCode: resultData.originalCreditCode || '',
                oldCompanyName: resultData.originalCompanyName || '',
                businessKey: resultData.businessKey || '',
                tableName: resultData.parsedTableName || currentConfigInfo?.tableName || '',
                businessKeyColumn: resultData.parsedBusinessKeyColumn || currentConfigInfo?.businessKeyColumn || '',

                // 向后兼容性 - 支持旧的占位符格式
                creditCode: company.creditCode || '',
                companyName: company.name || ''
            };

            // 替换占位符生成实际的更新SQL
            const actualUpdateSql = replacePlaceholders(updateSqlTemplate, placeholderData);

            console.log('执行更新SQL:', actualUpdateSql);

            // 调用后端API执行更新SQL
            const response = await fetch('/api/crpt/executeUpdate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `dbType=${encodeURIComponent(dbType)}&updateSql=${encodeURIComponent(actualUpdateSql)}`
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            const apiResult = await response.json();
            if (apiResult.code !== 200) {
                throw new Error(apiResult.message || '执行更新SQL失败');
            }

            console.log('更新SQL执行成功:', apiResult.message);
            return apiResult;

        } catch (error) {
            console.error('执行更新SQL失败:', error);
            throw error;
        }
    }

    // 执行回滚更新SQL语句（恢复到原始数据） - 从当前配置信息中实时获取SQL模板
    async function executeRollbackUpdateSql(resultData) {
        try {
            // 从日志文件名中提取数据库类型
            const dbType = extractDbTypeFromLogFileName(currentLogFileName);
            if (!dbType) {
                throw new Error('无法从日志文件名中提取数据库类型');
            }

            // 优先使用当前配置信息中的updateSql，如果没有则使用结果中的updateSql
            let updateSqlTemplate = null;

            if (currentConfigInfo && currentConfigInfo.updateSql) {
                updateSqlTemplate = currentConfigInfo.updateSql;
                console.log('回滚时使用当前配置信息中的更新SQL模板:', updateSqlTemplate);
            } else if (resultData.updateSql) {
                updateSqlTemplate = resultData.updateSql;
                console.log('回滚时使用日志记录中的更新SQL模板:', updateSqlTemplate);
            }

            if (!updateSqlTemplate) {
                throw new Error('未找到更新SQL模板');
            }

            // 获取当前选择的企业信息（要回滚掉的数据）
            const selectedCompany = selectedCompanyData[resultData.businessKey];

            // 准备占位符数据（回滚时的逻辑）
            const placeholderData = {
                // 新的语义化占位符 - 回滚时的正确映射
                newCreditCode: resultData.originalCreditCode || '',  // 回滚后的新值是原始数据
                newCompanyName: resultData.originalCompanyName || '', // 回滚后的新值是原始数据
                oldCreditCode: selectedCompany ? selectedCompany.creditCode : '', // 当前要被替换掉的值（选择的企业数据）
                oldCompanyName: selectedCompany ? selectedCompany.name : '', // 当前要被替换掉的值（选择的企业数据）
                businessKey: resultData.businessKey || '',
                tableName: resultData.parsedTableName || currentConfigInfo?.tableName || '',
                businessKeyColumn: resultData.parsedBusinessKeyColumn || currentConfigInfo?.businessKeyColumn || '',

                // 向后兼容性 - 支持旧的占位符格式（回滚时使用原始数据）
                creditCode: resultData.originalCreditCode || '',
                companyName: resultData.originalCompanyName || ''
            };

            // 替换占位符生成回滚SQL（使用原始数据）
            const rollbackSql = replacePlaceholders(updateSqlTemplate, placeholderData);

            console.log('执行回滚SQL:', rollbackSql);

            // 调用后端API执行回滚SQL
            const response = await fetch('/api/crpt/executeUpdate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `dbType=${encodeURIComponent(dbType)}&updateSql=${encodeURIComponent(rollbackSql)}`
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            const apiResult = await response.json();
            if (apiResult.code !== 200) {
                throw new Error(apiResult.message || '执行回滚SQL失败');
            }

            console.log('回滚SQL执行成功:', apiResult.message);
            return apiResult;

        } catch (error) {
            console.error('执行回滚SQL失败:', error);
            throw error;
        }
    }

    // 通用占位符替换函数
    function replacePlaceholders(sqlTemplate, placeholderData) {
        if (!sqlTemplate || !placeholderData) {
            return sqlTemplate;
        }

        let result = sqlTemplate;

        // 替换所有占位符
        Object.keys(placeholderData).forEach(key => {
            const value = placeholderData[key] || '';
            // 支持多种占位符格式
            result = result.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
            result = result.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), value);
            result = result.replace(new RegExp(`\\$\\{${key.toUpperCase()}\\}`, 'g'), value);
        });

        return result;
    }

    // 从日志文件名中提取数据库类型
    function extractDbTypeFromLogFileName(logFileName) {
        if (!logFileName) {
            return null;
        }

        // 日志文件名格式：{dbType}-{tableName}-{timestamp}.log
        const parts = logFileName.split('-');
        if (parts.length >= 3) {
            return parts[0]; // 第一部分是数据库类型
        }

        return null;
    }

    // 保存选择的企业信息到后端
    async function saveSelectedCompanyInfo(businessKey, company) {
        try {
            if (!currentLogFileName) {
                console.warn('当前日志文件名为空，无法保存选择信息');
                return;
            }

            const response = await fetch('/api/log/saveSelectedCompany', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    logFileName: currentLogFileName,
                    businessKey: businessKey,
                    selectedCompanyName: company.name,
                    selectedCreditCode: company.creditCode,
                    selectedCompanyType: company.companyType,
                    selectedBase: company.base,
                    selectedLegalPersonName: company.legalPersonName,
                    selectedRegStatus: company.regStatus,
                    jsonSource: company.source, // 记录JSON来源
                    jsonIndex: company.jsonIndex // 记录在原始JSON数组中的索引
                })
            });

            if (!response.ok) {
                console.warn('保存选择的企业信息失败:', response.statusText);
            }
        } catch (error) {
            console.error('保存选择的企业信息时发生错误:', error);
        }
    }

    // 关闭企业选择确认模态框
    function closeCompanyConfirmModal() {
        document.getElementById('companyConfirmModal').classList.add('hidden');
        //document.body.classList.remove('modal-open');
        pendingCompanySelection = null;

        // 重置处理中标志和按钮状态
        isConfirmingSelection = false;
        const confirmBtn = document.querySelector('#companyConfirmModal button[onclick="confirmCompanySelection()"]');
        if (confirmBtn) {
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="fas fa-check mr-2"></i>确认选择';
        }
    }

    // 撤销企业选择
    function unselectCompanyInModal(company, result, resultIndex) {
        const businessKey = result.businessKey;

        // 显示确认对话框
        showUnselectConfirmModal(company, result, resultIndex);
    }

    // 显示撤销选择确认模态框 - 包含回滚SQL展示
    function showUnselectConfirmModal(company, result, resultIndex) {
        const businessKey = result.businessKey; // 从result参数中提取businessKey

        // 生成回滚SQL预览
        const rollbackSqlPreview = generateRollbackSqlPreview(result);

        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 p-4';
        modal.id = 'unselectConfirmModal';

        modal.innerHTML = `
                <div class="relative mx-auto p-6 border w-full max-w-4xl max-h-full shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col modal-content">
                    <div class="flex-shrink-0">
                        <!-- 模态框头部 -->
                        <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2 text-orange-500"></i>确认撤销选择
                            </h3>
                            <button onclick="closeUnselectConfirmModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <div class="flex-1 min-h-0 overflow-y-auto mt-4 custom-scrollbar">
                        <div class="space-y-6">
                            <!-- 确认信息 -->
                            <div>
                                <p class="text-sm text-gray-700 dark:text-gray-300 mb-4">
                                    您确定要撤销对以下企业的选择吗？
                                </p>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${company.name || '-'}</div>
                                    <div class="text-xs text-gray-600 dark:text-gray-400">${company.creditCode || '-'}</div>
                                </div>
                                <p class="text-xs text-orange-600 dark:text-orange-400 mt-2">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    撤销后该记录将重新标记为"待处理"状态，数据库将回滚到原始状态。
                                </p>
                            </div>

                            <!-- 原始数据信息 -->
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                                <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                                    <i class="fas fa-database mr-2"></i>将回滚到原始数据
                                </h4>
                                <div class="grid md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <div class="text-blue-700 dark:text-blue-300 font-medium">原始企业名称:</div>
                                        <div class="text-blue-800 dark:text-blue-200">${result.originalCompanyName || '-'}</div>
                                    </div>
                                    <div>
                                        <div class="text-blue-700 dark:text-blue-300 font-medium">原始社会信用代码:</div>
                                        <div class="text-blue-800 dark:text-blue-200">${result.originalCreditCode || '-'}</div>
                                    </div>
                                    <div>
                                        <div class="text-blue-700 dark:text-blue-300 font-medium">业务主键:</div>
                                        <div class="text-blue-800 dark:text-blue-200">${result.businessKey || '-'}</div>
                                    </div>
                                    <div>
                                        <div class="text-blue-700 dark:text-blue-300 font-medium">表名:</div>
                                        <div class="text-blue-800 dark:text-blue-200">${result.parsedTableName || currentConfigInfo?.tableName || '-'}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 回滚SQL展示 -->
                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-md font-semibold text-red-900 dark:text-red-100 flex items-center">
                                        <i class="fas fa-code mr-2"></i>将执行的回滚SQL
                                    </h4>
                                    <button onclick="copyRollbackSql()" class="text-xs bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded transition-colors">
                                        <i class="fas fa-copy mr-1"></i>复制SQL
                                    </button>
                                </div>
                                <div id="rollbackSqlContent" class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm text-gray-900 dark:text-gray-100 overflow-x-auto max-h-40 overflow-y-auto">
                                    <pre class="whitespace-pre-wrap">${escapeHtml(rollbackSqlPreview)}</pre>
                                </div>
                                <p class="text-xs text-red-600 dark:text-red-400 mt-2">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    此SQL将把数据库中的记录恢复到选择企业之前的原始状态
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 模态框底部 -->
                    <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-600 mt-4 flex-shrink-0">
                        <div class="flex space-x-3">
                            <button onclick="closeUnselectConfirmModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors">
                                <i class="fas fa-times mr-2"></i>取消
                            </button>
                            <button onclick="confirmUnselectCompany('${businessKey}', ${resultIndex})" class="bg-orange-500 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded transition-colors">
                                <i class="fas fa-undo mr-2"></i>确认撤销
                            </button>
                        </div>
                    </div>
                </div>
            `;

        document.body.appendChild(modal);
        document.body.classList.add('modal-open');

        // 保存回滚SQL到全局变量以便复制
        window.currentRollbackSql = rollbackSqlPreview;
    }

    // 生成回滚SQL预览
    function generateRollbackSqlPreview(resultData) {
        try {
            // 优先使用当前配置信息中的updateSql，如果没有则使用结果中的updateSql
            let updateSqlTemplate = null;

            if (currentConfigInfo && currentConfigInfo.updateSql) {
                updateSqlTemplate = currentConfigInfo.updateSql;
            } else if (resultData.updateSql) {
                updateSqlTemplate = resultData.updateSql;
            }

            if (!updateSqlTemplate) {
                return '未找到更新SQL模板';
            }

            // 获取当前选择的企业信息（要回滚掉的数据）
            const selectedCompany = selectedCompanyData[resultData.businessKey];

            // 准备占位符数据（回滚时的逻辑）
            const placeholderData = {
                // 新的语义化占位符 - 回滚时的正确映射
                newCreditCode: resultData.originalCreditCode || '',  // 回滚后的新值是原始数据
                newCompanyName: resultData.originalCompanyName || '', // 回滚后的新值是原始数据
                oldCreditCode: selectedCompany ? selectedCompany.creditCode : '', // 当前要被替换掉的值（选择的企业数据）
                oldCompanyName: selectedCompany ? selectedCompany.name : '', // 当前要被替换掉的值（选择的企业数据）
                businessKey: resultData.businessKey || '',
                tableName: resultData.parsedTableName || currentConfigInfo?.tableName || '',
                businessKeyColumn: resultData.parsedBusinessKeyColumn || currentConfigInfo?.businessKeyColumn || '',

                // 向后兼容性 - 支持旧的占位符格式（回滚时使用原始数据）
                creditCode: resultData.originalCreditCode || '',
                companyName: resultData.originalCompanyName || ''
            };

            // 替换占位符生成回滚SQL
            const rollbackSql = replacePlaceholders(updateSqlTemplate, placeholderData);

            return rollbackSql;
        } catch (error) {
            console.error('生成回滚SQL预览失败:', error);
            return '生成回滚SQL预览失败: ' + error.message;
        }
    }

    // 复制回滚SQL
    function copyRollbackSql() {
        if (window.currentRollbackSql) {
            navigator.clipboard.writeText(window.currentRollbackSql).then(() => {
                showMessage('回滚SQL已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                showMessage('复制失败，请手动复制', 'error');
            });
        } else {
            showMessage('没有可复制的回滚SQL', 'warning');
        }
    }

    // 关闭撤销选择确认模态框
    function closeUnselectConfirmModal() {
        const modal = document.getElementById('unselectConfirmModal');
        if (modal) {
            document.body.removeChild(modal);
            //document.body.classList.remove('modal-open');
        }

        // 重置处理中标志
        isConfirmingUnselection = false;
    }

    // 确认撤销企业选择
    async function confirmUnselectCompany(businessKey, resultIndex) {
        // 防重复点击
        if (isConfirmingUnselection) {
            showMessage('正在处理中，请稍候...', 'warning');
            return;
        }

        try {
            // 设置处理中标志
            isConfirmingUnselection = true;

            // 禁用确认按钮并显示加载状态
            const confirmBtn = document.querySelector(`button[onclick="confirmUnselectCompany('${businessKey}', ${resultIndex})"]`);
            if (confirmBtn) {
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';
            }

            // 获取原始数据用于回滚
            const result = currentMatchResults.find(r => r.businessKey === businessKey);
            if (!result) {
                throw new Error('未找到对应的匹配结果数据');
            }

            // 执行回滚更新SQL，将数据恢复到原始状态
            await executeRollbackUpdateSql(result);

            // 清除前端选择状态
            delete selectedCompanyData[businessKey];

            // 调用后端API取消已处理标记
            await unmarkAsProcessed(businessKey);

            // 删除选择的企业信息
            await deleteSelectedCompanyInfo(businessKey);

            // 重新渲染企业选择列表
            if (result) {
                displayCompanySelectionInModal(result, resultIndex);
            }

            // 关闭确认模态框
            closeUnselectConfirmModal();

            // 显示成功消息
            showMessage('已撤销企业选择，数据已回滚到原始状态，记录重新标记为待处理状态', 'success');

            // 刷新表格数据以更新处理状态
            if (currentLogFileName) {
                analyzeLog();
            }

        } catch (error) {
            console.error('撤销企业选择失败:', error);
            showMessage('撤销选择失败: ' + error.message, 'error');
        } finally {
            // 重置处理中标志和按钮状态
            isConfirmingUnselection = false;
            const confirmBtn = document.querySelector(`button[onclick="confirmUnselectCompany('${businessKey}', ${resultIndex})"]`);
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="fas fa-undo mr-2"></i>确认撤销';
            }
        }
    }

    // 取消已处理标记
    async function unmarkAsProcessed(businessKey) {
        const response = await fetch('/api/processed/unmark', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `logFileName=${encodeURIComponent(currentLogFileName)}&businessKey=${encodeURIComponent(businessKey)}`
        });

        if (!response.ok) {
            throw new Error('取消已处理标记失败: ' + response.statusText);
        }
    }

    // 删除选择的企业信息
    async function deleteSelectedCompanyInfo(businessKey) {
        try {
            if (!currentLogFileName) {
                console.warn('当前日志文件名为空，无法删除选择信息');
                return;
            }

            const response = await fetch('/api/log/deleteSelectedCompany', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    logFileName: currentLogFileName,
                    businessKey: businessKey
                })
            });

            if (!response.ok) {
                console.warn('删除选择的企业信息失败:', response.statusText);
            }
        } catch (error) {
            console.error('删除选择的企业信息时发生错误:', error);
        }
    }

    // 企业类型映射函数
    function getCompanyTypeDisplay(companyType) {
        if (!companyType) return '';

        // 如果已经是中文描述，直接返回
        if (!/^\d+$/.test(companyType)) {
            return companyType;
        }

        // 数值映射到中文描述
        const typeMap = {
            '1': '有限责任公司',
            '2': '股份有限公司',
            '3': '国有独资公司',
            '4': '外商投资企业',
            '5': '个人独资企业',
            '6': '合伙企业',
            '7': '农民专业合作社',
            '8': '个体工商户',
            '9': '其他企业',
            '10': '事业单位',
            '11': '社会团体',
            '12': '民办非企业单位',
            '13': '基金会',
            '14': '律师事务所',
            '15': '会计师事务所'
        };

        return typeMap[companyType] || `未知类型(${companyType})`;
    }

    // 企业状态显示函数
    function getCompanyStatusDisplay(regStatus) {
        if (!regStatus) return '';
        return regStatus;
    }

    // 从JSON数据中提取企业信息
    function extractCompaniesFromJson(creditCodeJson, companyNameJson) {
        const companies = [];
        const seenCompanies = new Set(); // 用于去重

        // 解析社会信用代码查询结果
        try {
            const creditCodeData = JSON.parse(creditCodeJson);
            if (creditCodeData.result && creditCodeData.result.items && Array.isArray(creditCodeData.result.items)) {
                creditCodeData.result.items.forEach((item, index) => {
                    if (item.name && item.creditCode) {
                        const key = `${item.name}_${item.creditCode}`;
                        if (!seenCompanies.has(key)) {
                            seenCompanies.add(key);
                            companies.push({
                                name: item.name,
                                creditCode: item.creditCode,
                                regStatus: item.regStatus,
                                companyType: item.companyType,
                                base: item.base,
                                legalPersonName: item.legalPersonName,
                                source: 'creditCode',
                                jsonIndex: index // 记录在原始JSON数组中的索引
                            });
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('解析社会信用代码查询结果失败:', error);
        }

        // 解析单位名称查询结果
        try {
            const companyNameData = JSON.parse(companyNameJson);
            if (companyNameData.result && companyNameData.result.items && Array.isArray(companyNameData.result.items)) {
                companyNameData.result.items.forEach((item, index) => {
                    if (item.name && item.creditCode) {
                        const key = `${item.name}_${item.creditCode}`;
                        if (!seenCompanies.has(key)) {
                            seenCompanies.add(key);
                            companies.push({
                                name: item.name,
                                creditCode: item.creditCode,
                                regStatus: item.regStatus,
                                companyType: item.companyType,
                                base: item.base,
                                legalPersonName: item.legalPersonName,
                                source: 'companyName',
                                jsonIndex: index // 记录在原始JSON数组中的索引
                            });
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('解析单位名称查询结果失败:', error);
        }

        return companies;
    }

    // 关闭编辑更新SQL模态框
    function closeEditUpdateSqlModal() {
        document.getElementById('editUpdateSqlModal').classList.add('hidden');
        document.body.classList.remove('modal-open');
    }

    // 重置更新SQL
    function resetUpdateSql() {
        if (currentConfigInfo && currentConfigInfo.updateSql) {
            document.getElementById('editUpdateSqlTextarea').value = currentConfigInfo.updateSql;
            showMessage('已重置为原始SQL', 'info');
        }
    }

    // 预览更新SQL
    function previewUpdateSql() {
        const sqlTemplate = document.getElementById('editUpdateSqlTextarea').value.trim();

        if (!sqlTemplate) {
            showMessage('请输入更新SQL模板', 'warning');
            return;
        }

        // 使用示例数据进行预览
        const sampleData = {
            tableName: currentConfigInfo?.tableName || 'TB_TJ_CRPT_TEMP',
            newCreditCode: '91320585608268400P',
            newCompanyName: '苏州铃兰医疗用品有限公司',
            oldCreditCode: '原始社会信用代码',
            oldCompanyName: '原始企业名称',
            businessKey: '5868',
            businessKeyColumn: currentConfigInfo?.businessKeyColumn || 'RID'
        };

        // 替换占位符
        let previewSql = sqlTemplate;
        Object.keys(sampleData).forEach(key => {
            const value = sampleData[key] || '';
            previewSql = previewSql.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
            previewSql = previewSql.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), value);
            previewSql = previewSql.replace(new RegExp(`\\$\\{${key.toUpperCase()}\\}`, 'g'), value);
        });

        // 显示预览
        document.getElementById('sqlPreviewContent').textContent = previewSql;
        document.getElementById('sqlPreviewSection').classList.remove('hidden');
    }

    // 保存更新SQL
    async function saveUpdateSql() {
        const newUpdateSql = document.getElementById('editUpdateSqlTextarea').value.trim();

        if (!newUpdateSql) {
            showMessage('请输入更新SQL模板', 'warning');
            return;
        }

        if (!currentLogFileName) {
            showMessage('未选择日志文件', 'error');
            return;
        }

        try {
            const response = await fetch('/api/log/updateConfig', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    updateSql: newUpdateSql
                }),
                params: new URLSearchParams({fileName: currentLogFileName})
            });

            // 修正请求方式
            const actualResponse = await fetch(`/api/log/updateConfig?fileName=${encodeURIComponent(currentLogFileName)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    updateSql: newUpdateSql
                })
            });

            const result = await actualResponse.json();

            if (result.code === 200) {
                // 更新本地配置信息
                if (currentConfigInfo) {
                    currentConfigInfo.updateSql = newUpdateSql;
                }

                // 关闭模态框
                closeEditUpdateSqlModal();

                // 重新分析日志以刷新配置信息显示
                await analyzeLog();

                showMessage('更新SQL保存成功', 'success');
            } else {
                showMessage(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存更新SQL失败:', error);
            showMessage('保存失败: ' + error.message, 'error');
        }
    }

    // 打开用户操作手册
    function openUserManual() {
        // 在新标签页中打开操作手册页面
        window.open('/manual.html', '_blank');
    }


</script>
</body>
</html>
