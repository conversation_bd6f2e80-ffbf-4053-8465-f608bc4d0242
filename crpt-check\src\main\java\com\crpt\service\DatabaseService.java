package com.crpt.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库服务
 */
@Service
public class DatabaseService {

    private static final Logger log = LoggerFactory.getLogger(DatabaseService.class);
    
    @Value("${spring.datasource.mysql.driver-class-name}")
    private String mysqlDriverClass;
    
    @Value("${spring.datasource.mysql.url}")
    private String mysqlUrl;
    
    @Value("${spring.datasource.mysql.username}")
    private String mysqlUsername;
    
    @Value("${spring.datasource.mysql.password}")
    private String mysqlPassword;
    
    @Value("${spring.datasource.oracle.driver-class-name}")
    private String oracleDriverClass;
    
    @Value("${spring.datasource.oracle.url}")
    private String oracleUrl;
    
    @Value("${spring.datasource.oracle.username}")
    private String oracleUsername;
    
    @Value("${spring.datasource.oracle.password}")
    private String oraclePassword;
    
    /**
     * 获取数据库连接
     */
    public Connection getConnection(String dbType) throws SQLException {
        try {
            if ("mysql".equalsIgnoreCase(dbType)) {
                Class.forName(mysqlDriverClass);
                return DriverManager.getConnection(mysqlUrl, mysqlUsername, mysqlPassword);
            } else if ("oracle".equalsIgnoreCase(dbType)) {
                Class.forName(oracleDriverClass);
                return DriverManager.getConnection(oracleUrl, oracleUsername, oraclePassword);
            } else {
                throw new IllegalArgumentException("不支持的数据库类型: " + dbType);
            }
        } catch (ClassNotFoundException e) {
            throw new SQLException("数据库驱动加载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 分页查询数据
     */
    public List<Map<String, Object>> queryData(String dbType, String sql, long lastId, int pageSize) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection connection = getConnection(dbType)) {
            // 替换SQL中的占位符，支持多种常见的占位符格式
            String executeSql = replacePlaceholders(sql, lastId);
            
            // 添加分页限制
            if ("mysql".equalsIgnoreCase(dbType)) {
                executeSql += " LIMIT " + pageSize;
            } else if ("oracle".equalsIgnoreCase(dbType)) {
                executeSql = "SELECT * FROM (" + executeSql + ") WHERE ROWNUM <= " + pageSize;
            }
            
            log.info("执行SQL: {}", executeSql);
            
            try (PreparedStatement statement = connection.prepareStatement(executeSql);
                 ResultSet resultSet = statement.executeQuery()) {
                
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                while (resultSet.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnLabel(i);
                        Object value = resultSet.getObject(i);
                        row.put(columnName, value);
                    }
                    results.add(row);
                }
            }
        } catch (SQLException e) {
            log.error("数据库查询失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据库查询失败: " + e.getMessage(), e);
        }
        
        return results;
    }

    /**
     * 执行更新SQL语句（支持多个SQL语句）
     */
    public int executeUpdate(String dbType, String updateSql) {
        if (!StringUtils.hasText(updateSql)) {
            throw new IllegalArgumentException("更新SQL不能为空");
        }

        try (Connection connection = getConnection(dbType)) {
            log.info("执行更新SQL: {}", updateSql);

            // 分割多个SQL语句（以分号分隔，但要去除空白语句）
            String[] sqlStatements = updateSql.split(";");
            int totalAffectedRows = 0;

            for (String sql : sqlStatements) {
                String trimmedSql = sql.trim();
                if (StringUtils.hasText(trimmedSql)) {
                    log.debug("执行单个SQL语句: {}", trimmedSql);

                    try (PreparedStatement statement = connection.prepareStatement(trimmedSql)) {
                        int affectedRows = statement.executeUpdate();
                        totalAffectedRows += affectedRows;
                        log.debug("SQL语句执行完成，影响行数: {}", affectedRows);
                    }
                }
            }

            log.info("所有更新SQL执行完成，总影响行数: {}", totalAffectedRows);
            return totalAffectedRows;

        } catch (SQLException e) {
            log.error("执行更新SQL失败: {}", e.getMessage(), e);
            throw new RuntimeException("执行更新SQL失败: " + e.getMessage(), e);
        }
    }

    /**
     * 替换SQL中的占位符，支持多种常见格式
     * 支持的占位符格式：#{ID}、#{id}、#{RID}、#{rid}等
     */
    private String replacePlaceholders(String sql, long lastId) {
        if (sql == null) {
            return null;
        }

        String result = sql;
        String lastIdStr = String.valueOf(lastId);

        // 支持常见的占位符格式
        result = result.replace("#{ID}", lastIdStr);
        result = result.replace("#{id}", lastIdStr);
        result = result.replace("#{RID}", lastIdStr);
        result = result.replace("#{rid}", lastIdStr);
        result = result.replace("#{Rid}", lastIdStr);
        result = result.replace("#{Id}", lastIdStr);

        // 使用正则表达式处理其他可能的占位符格式
        // 匹配 #{任意字符} 格式的占位符
        result = result.replaceAll("#\\{[^}]+\\}", lastIdStr);

        return result;
    }
}
