<!DOCTYPE html>
<html>
<head>
    <title>测试注释处理</title>
</head>
<body>
    <h1>测试Oracle DDL注释处理</h1>
    
    <h2>测试用例1：包含换行符的表注释</h2>
    <textarea id="test1" rows="10" cols="80">
CREATE TABLE test_table (
    id NUMBER(10) NOT NULL,
    name VARCHAR2(100) COMMENT '用户姓名
这是第二行注释
这是第三行注释',
    age NUMBER(3) COMMENT '年龄信息'
);

COMMENT ON TABLE test_table IS '这是表注释
包含多行内容
用于测试换行符处理';

COMMENT ON COLUMN test_table.name IS '姓名列注释
包含换行符
需要被替换为空格';
    </textarea>
    
    <h2>测试用例2：MySQL DDL注释</h2>
    <textarea id="test2" rows="8" cols="80">
CREATE TABLE test_table (
    id INT PRIMARY KEY,
    name VARCHAR(100) COMMENT '用户姓名
包含换行符的注释',
    age INT COMMENT '年龄'
) COMMENT='表注释
包含多行
内容测试';
    </textarea>
    
    <button onclick="testCommentHandling()">测试注释处理</button>
    
    <h2>处理结果：</h2>
    <div id="result"></div>
    
    <script>
        function testCommentHandling() {
            // 测试注释清理函数
            function cleanComment(comment) {
                return comment.replace(/\s+/g, ' ').trim();
            }
            
            const testCases = [
                "这是单行注释",
                "这是多行注释\n包含换行符",
                "这是\n多个\n换行符\n的注释",
                "这是\t包含\t制表符\t的注释",
                "这是   包含   多个   空格   的注释",
                "  前后有空格的注释  ",
                "混合\n\t  各种\n空白字符\t\n的注释  "
            ];
            
            let result = "<h3>注释清理测试结果：</h3>";
            testCases.forEach((testCase, index) => {
                const cleaned = cleanComment(testCase);
                result += `<p><strong>测试${index + 1}:</strong><br>`;
                result += `原始: "${testCase.replace(/\n/g, '\\n').replace(/\t/g, '\\t')}"<br>`;
                result += `清理后: "${cleaned}"</p>`;
            });
            
            document.getElementById('result').innerHTML = result;
        }
    </script>
</body>
</html>
