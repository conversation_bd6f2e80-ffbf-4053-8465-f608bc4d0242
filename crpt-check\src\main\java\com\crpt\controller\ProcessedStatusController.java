package com.crpt.controller;

import com.crpt.entity.ApiResponse;
import com.crpt.service.ProcessedStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 处理状态控制器
 * 提供记录处理状态的管理接口
 */
@RestController
@RequestMapping("/api/processed")
public class ProcessedStatusController {

    private static final Logger log = LoggerFactory.getLogger(ProcessedStatusController.class);

    @Autowired
    private ProcessedStatusService processedStatusService;

    /**
     * 标记记录为已处理
     */
    @PostMapping("/mark")
    public ApiResponse<String> markAsProcessed(
            @RequestParam String logFileName,
            @RequestParam String businessKey) {
        
        try {
            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                return ApiResponse.error(400, "日志文件名不能为空");
            }
            if (!StringUtils.hasText(businessKey)) {
                return ApiResponse.error(400, "业务主键不能为空");
            }

            // 标记为已处理
            processedStatusService.markAsProcessed(logFileName, businessKey);
            
            log.info("标记记录为已处理成功 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);
            return ApiResponse.success("标记为已处理成功");
            
        } catch (Exception e) {
            log.error("标记记录为已处理失败", e);
            return ApiResponse.error(500, "标记失败: " + e.getMessage());
        }
    }

    /**
     * 取消已处理标记
     */
    @PostMapping("/unmark")
    public ApiResponse<String> unmarkAsProcessed(
            @RequestParam String logFileName,
            @RequestParam String businessKey) {
        
        try {
            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                return ApiResponse.error(400, "日志文件名不能为空");
            }
            if (!StringUtils.hasText(businessKey)) {
                return ApiResponse.error(400, "业务主键不能为空");
            }

            // 取消已处理标记
            processedStatusService.unmarkAsProcessed(logFileName, businessKey);
            
            log.info("取消已处理标记成功 - 日志文件: {}, 业务主键: {}", logFileName, businessKey);
            return ApiResponse.success("取消已处理标记成功");
            
        } catch (Exception e) {
            log.error("取消已处理标记失败", e);
            return ApiResponse.error(500, "取消标记失败: " + e.getMessage());
        }
    }

    /**
     * 检查记录是否已处理
     */
    @GetMapping("/check")
    public ApiResponse<Boolean> checkProcessedStatus(
            @RequestParam String logFileName,
            @RequestParam String businessKey) {
        
        try {
            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                return ApiResponse.error(400, "日志文件名不能为空");
            }
            if (!StringUtils.hasText(businessKey)) {
                return ApiResponse.error(400, "业务主键不能为空");
            }

            // 检查处理状态
            boolean isProcessed = processedStatusService.isProcessed(logFileName, businessKey);
            
            return ApiResponse.success(isProcessed);
            
        } catch (Exception e) {
            log.error("检查处理状态失败", e);
            return ApiResponse.error(500, "检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定日志文件的所有处理状态
     */
    @GetMapping("/all")
    public ApiResponse<Map<String, Boolean>> getAllProcessedStatus(
            @RequestParam String logFileName) {
        
        try {
            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                return ApiResponse.error(400, "日志文件名不能为空");
            }

            // 获取所有处理状态
            Map<String, Boolean> allStatus = processedStatusService.getAllProcessedStatus(logFileName);
            
            return ApiResponse.success(allStatus);
            
        } catch (Exception e) {
            log.error("获取处理状态失败", e);
            return ApiResponse.error(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 清除指定日志文件的所有处理状态
     */
    @PostMapping("/clear")
    public ApiResponse<String> clearAllProcessedStatus(
            @RequestParam String logFileName) {
        
        try {
            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                return ApiResponse.error(400, "日志文件名不能为空");
            }

            // 清除所有处理状态
            processedStatusService.clearAllProcessedStatus(logFileName);
            
            log.info("清除所有处理状态成功 - 日志文件: {}", logFileName);
            return ApiResponse.success("清除所有处理状态成功");
            
        } catch (Exception e) {
            log.error("清除处理状态失败", e);
            return ApiResponse.error(500, "清除失败: " + e.getMessage());
        }
    }

    /**
     * 获取处理状态统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getProcessedStatistics(
            @RequestParam String logFileName) {
        
        try {
            // 参数校验
            if (!StringUtils.hasText(logFileName)) {
                return ApiResponse.error(400, "日志文件名不能为空");
            }

            // 获取统计信息
            Map<String, Object> statistics = processedStatusService.getProcessedStatistics(logFileName);
            
            return ApiResponse.success(statistics);
            
        } catch (Exception e) {
            log.error("获取处理状态统计失败", e);
            return ApiResponse.error(500, "获取统计失败: " + e.getMessage());
        }
    }
}
