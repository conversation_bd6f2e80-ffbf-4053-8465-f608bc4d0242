<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="aliyun_id" />
      <option name="name" value="aliyun_id" />
      <option name="url" value="http://10.88.99.21:8081/nexus/content/repository/JxcDomain/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="chisframe" />
      <option name="name" value="chisframe" />
      <option name="url" value="http://10.88.99.21:8081/nexus/content/repository/JxcDomain/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun_central" />
      <option name="name" value="aliyun_central" />
      <option name="url" value="http://10.88.99.21:8081/nexus/content/repository/JxcDomain/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central" />
      <option name="url" value="http://10.88.99.21:8081/nexus/content/repository/JxcDomain/" />
    </remote-repository>
  </component>
</project>