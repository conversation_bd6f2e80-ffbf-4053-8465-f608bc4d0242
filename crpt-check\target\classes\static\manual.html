<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户操作手册 - 天眼查企业信息匹配系统</title>
    <script src="assets/js/tailwind.min.js"></script>
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <!-- Marked.js for Markdown rendering -->
    <script src="assets/js/marked.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Markdown 渲染样式 */
        .markdown-content {
            line-height: 1.7;
            color: #374151;
        }
        .dark .markdown-content {
            color: #e5e7eb;
        }
        
        .markdown-content h1 {
            font-size: 2.25rem;
            font-weight: 700;
            margin: 2rem 0 1rem 0;
            color: #1f2937;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 0.5rem;
        }
        .dark .markdown-content h1 {
            color: #f9fafb;
            border-bottom-color: #60a5fa;
        }
        
        .markdown-content h2 {
            font-size: 1.875rem;
            font-weight: 600;
            margin: 1.5rem 0 0.75rem 0;
            color: #1f2937;
        }
        .dark .markdown-content h2 {
            color: #f9fafb;
        }
        
        .markdown-content h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 1.25rem 0 0.5rem 0;
            color: #374151;
        }
        .dark .markdown-content h3 {
            color: #e5e7eb;
        }
        
        .markdown-content h4 {
            font-size: 1.25rem;
            font-weight: 500;
            margin: 1rem 0 0.5rem 0;
            color: #4b5563;
        }
        .dark .markdown-content h4 {
            color: #d1d5db;
        }
        
        .markdown-content p {
            margin: 0.75rem 0;
        }
        
        .markdown-content ul, .markdown-content ol {
            margin: 0.75rem 0;
            padding-left: 1.5rem;
        }
        
        .markdown-content li {
            margin: 0.25rem 0;
        }
        
        .markdown-content strong {
            font-weight: 600;
            color: #111827;
        }
        .dark .markdown-content strong {
            color: #f3f4f6;
        }
        
        .markdown-content code {
            background-color: #f3f4f6;
            color: #1f2937;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .dark .markdown-content code {
            background-color: #374151;
            color: #e5e7eb;
        }
        
        .markdown-content pre {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .dark .markdown-content pre {
            background-color: #1f2937;
            border-color: #374151;
        }
        
        .markdown-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: 0.875rem;
            line-height: 1.5;
        }
        
        .markdown-content blockquote {
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: #6b7280;
        }
        .dark .markdown-content blockquote {
            border-left-color: #60a5fa;
            color: #9ca3af;
        }
        
        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        
        .markdown-content th, .markdown-content td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem 0.75rem;
            text-align: left;
        }
        .dark .markdown-content th, .dark .markdown-content td {
            border-color: #4b5563;
        }
        
        .markdown-content th {
            background-color: #f9fafb;
            font-weight: 600;
        }
        .dark .markdown-content th {
            background-color: #374151;
        }

        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .dark ::-webkit-scrollbar-track {
            background: #374151;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .dark ::-webkit-scrollbar-thumb {
            background: #6b7280;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* 布局样式 */
        .manual-container {
            display: flex;
            min-height: calc(100vh - 4rem);
        }

        /* 左侧目录样式 */
        .toc-sidebar {
            width: 280px;
            background-color: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem 1rem;
            position: sticky;
            top: 4rem;
            height: calc(100vh - 4rem);
            overflow-y: auto;
            flex-shrink: 0;
        }
        .dark .toc-sidebar {
            background-color: #1f2937;
            border-right-color: #374151;
        }

        /* 目录滚动条样式 */
        .toc-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .toc-sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .toc-sidebar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .toc-sidebar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .dark .toc-sidebar::-webkit-scrollbar-thumb {
            background: #4b5563;
        }

        .dark .toc-sidebar::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }

        .toc-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 0.5rem;
        }
        .dark .toc-title {
            color: #f9fafb;
            border-bottom-color: #60a5fa;
        }

        .toc ul {
            list-style: none;
            padding-left: 0;
            margin: 0;
        }

        .toc ul ul {
            padding-left: 1rem;
            margin-top: 0.25rem;
        }

        .toc li {
            margin: 0;
        }

        .toc a {
            color: #4b5563;
            text-decoration: none;
            display: block;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            transition: all 0.2s;
            font-size: 0.875rem;
            line-height: 1.4;
            border-left: 3px solid transparent;
            position: relative;
        }

        .toc a:hover {
            background-color: #e0f2fe;
            color: #0369a1;
            border-left-color: #3b82f6;
            transform: translateX(2px);
        }

        .toc a.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            border-left-color: #3b82f6;
            font-weight: 500;
            transform: translateX(2px);
        }

        .toc a.active::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
            border-radius: 0 2px 2px 0;
        }

        .dark .toc a {
            color: #d1d5db;
        }

        .dark .toc a:hover {
            background-color: #374151;
            color: #60a5fa;
            border-left-color: #60a5fa;
        }

        .dark .toc a.active {
            background-color: #1e3a8a;
            color: #93c5fd;
            border-left-color: #60a5fa;
        }

        .dark .toc a.active::before {
            background: linear-gradient(to bottom, #60a5fa, #93c5fd);
        }

        /* 目录层级缩进 */
        .toc ul ul a {
            padding-left: 1.5rem;
            font-size: 0.8125rem;
        }

        .toc ul ul ul a {
            padding-left: 2.25rem;
            font-size: 0.75rem;
        }

        /* 主要内容区域 */
        .content-area {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }

        /* 移动端响应式 */
        @media (max-width: 768px) {
            .manual-container {
                flex-direction: column;
            }

            .toc-sidebar {
                width: 100%;
                height: auto;
                position: static;
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
                max-height: 200px;
            }

            .dark .toc-sidebar {
                border-bottom-color: #374151;
            }

            .content-area {
                padding: 1rem;
            }
        }

        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }

        /* 回到顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }

        .dark .back-to-top {
            background-color: #60a5fa;
        }

        .dark .back-to-top:hover {
            background-color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- 导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <i class="fas fa-book text-2xl text-primary-600 mr-3"></i>
                    <h1 class="text-xl font-semibold text-gray-900 dark:text-white">用户操作手册</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="window.close()" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回主页
                    </button>
                    <button id="themeToggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="flex-1">
        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-12">
            <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-primary-500 transition ease-in-out duration-150">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                正在加载操作手册...
            </div>
        </div>

        <!-- 错误状态 -->
        <div id="errorState" class="hidden text-center py-12">
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-6 mx-4">
                <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">加载失败</h3>
                <p class="text-red-600 dark:text-red-300 mb-4" id="errorMessage">无法加载操作手册，请稍后重试。</p>
                <button onclick="loadManual()" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    重新加载
                </button>
            </div>
        </div>

        <!-- 手册内容容器 -->
        <div id="manualContent" class="hidden fade-in manual-container">
            <!-- 左侧目录 -->
            <aside class="toc-sidebar">
                <div class="toc-title">
                    <i class="fas fa-list mr-2"></i>目录
                </div>
                <nav id="tocNavigation" class="toc">
                    <!-- 目录将在这里动态生成 -->
                </nav>
            </aside>

            <!-- 右侧内容区域 -->
            <div class="content-area">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                    <div id="markdownContent" class="markdown-content"></div>
                </div>
            </div>
        </div>
    </main>

    <!-- 回到顶部按钮 -->
    <button id="backToTop" class="back-to-top" onclick="scrollToTop()" title="回到顶部">
        <i class="fas fa-chevron-up"></i>
    </button>

    <script>
        // 主题切换
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;

        // 初始化主题
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }

        themeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.theme = html.classList.contains('dark') ? 'dark' : 'light';
        });

        // 加载操作手册
        async function loadManual() {
            const loadingState = document.getElementById('loadingState');
            const errorState = document.getElementById('errorState');
            const manualContent = document.getElementById('manualContent');
            const markdownContent = document.getElementById('markdownContent');

            // 显示加载状态
            loadingState.classList.remove('hidden');
            errorState.classList.add('hidden');
            manualContent.classList.add('hidden');

            try {
                const response = await fetch('/docs/用户操作手册.md');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const markdownText = await response.text();

                // 使用marked.js渲染markdown
                if (typeof marked !== 'undefined') {
                    const htmlContent = marked.parse(markdownText);
                    markdownContent.innerHTML = htmlContent;
                } else {
                    // 如果marked.js未加载，显示原始文本
                    markdownContent.innerHTML = '<pre>' + markdownText + '</pre>';
                }

                // 隐藏加载状态，显示内容
                loadingState.classList.add('hidden');
                manualContent.classList.remove('hidden');

                // 添加目录导航
                generateTOC();

            } catch (error) {
                console.error('加载操作手册失败:', error);
                
                // 显示错误状态
                loadingState.classList.add('hidden');
                document.getElementById('errorMessage').textContent = error.message;
                errorState.classList.remove('hidden');
            }
        }

        // 生成目录
        function generateTOC() {
            const headings = document.querySelectorAll('#markdownContent h1, #markdownContent h2, #markdownContent h3, #markdownContent h4');
            if (headings.length === 0) return;

            const tocNavigation = document.getElementById('tocNavigation');
            const tocList = document.createElement('ul');

            headings.forEach((heading, index) => {
                // 为标题添加ID
                const id = `heading-${index}`;
                heading.id = id;

                // 创建目录项
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = `#${id}`;
                a.textContent = heading.textContent;
                a.dataset.target = id;

                // 根据标题级别设置样式
                const level = parseInt(heading.tagName.substring(1));
                if (level === 1) {
                    a.className = 'font-semibold';
                } else if (level === 2) {
                    a.className = 'font-medium';
                }

                // 添加点击事件
                a.addEventListener('click', function(e) {
                    e.preventDefault();
                    scrollToHeading(id);
                    updateActiveLink(a);
                });

                li.appendChild(a);

                // 根据标题级别创建嵌套结构
                if (level > 2) {
                    // 查找最近的父级li
                    const parentLi = findParentLi(tocList, level);
                    if (parentLi) {
                        let subList = parentLi.querySelector('ul');
                        if (!subList) {
                            subList = document.createElement('ul');
                            parentLi.appendChild(subList);
                        }
                        subList.appendChild(li);
                    } else {
                        tocList.appendChild(li);
                    }
                } else {
                    tocList.appendChild(li);
                }
            });

            tocNavigation.appendChild(tocList);

            // 初始化滚动监听
            initScrollSpy();
        }

        // 查找父级li元素
        function findParentLi(tocList, currentLevel) {
            const allLis = tocList.querySelectorAll('li');
            for (let i = allLis.length - 1; i >= 0; i--) {
                const li = allLis[i];
                const link = li.querySelector('a');
                if (link) {
                    const targetId = link.dataset.target;
                    const targetHeading = document.getElementById(targetId);
                    if (targetHeading) {
                        const targetLevel = parseInt(targetHeading.tagName.substring(1));
                        if (targetLevel < currentLevel) {
                            return li;
                        }
                    }
                }
            }
            return null;
        }

        // 滚动到指定标题
        function scrollToHeading(id) {
            const element = document.getElementById(id);
            if (element) {
                const offsetTop = element.offsetTop - 100; // 留出一些空间
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        }

        // 更新活动链接
        function updateActiveLink(activeLink) {
            // 移除所有活动状态
            document.querySelectorAll('.toc a').forEach(link => {
                link.classList.remove('active');
            });

            // 添加活动状态
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // 初始化滚动监听
        function initScrollSpy() {
            const headings = document.querySelectorAll('#markdownContent h1, #markdownContent h2, #markdownContent h3, #markdownContent h4');
            const tocLinks = document.querySelectorAll('.toc a');

            if (headings.length === 0 || tocLinks.length === 0) return;

            // 创建Intersection Observer
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const id = entry.target.id;
                        const activeLink = document.querySelector(`.toc a[data-target="${id}"]`);
                        updateActiveLink(activeLink);
                    }
                });
            }, {
                rootMargin: '-100px 0px -50% 0px',
                threshold: 0
            });

            // 观察所有标题
            headings.forEach(heading => {
                observer.observe(heading);
            });
        }

        // 回到顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 监听滚动事件，控制回到顶部按钮显示
        function initBackToTopButton() {
            const backToTopButton = document.getElementById('backToTop');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.add('visible');
                } else {
                    backToTopButton.classList.remove('visible');
                }
            });
        }

        // 页面加载完成后自动加载手册
        document.addEventListener('DOMContentLoaded', () => {
            loadManual();
            initBackToTopButton();
        });
    </script>
</body>
</html>
