package com.crpt.entity;

/**
 * 匹配结果实体类
 */
public class MatchResult {

    /**
     * 业务主键
     */
    private String businessKey;

    /**
     * 原始单位名称
     */
    private String originalCompanyName;

    /**
     * 原始社会信用代码
     */
    private String originalCreditCode;

    /**
     * 原始删除标记
     */
    private String originalDelMark;

    /**
     * 原始是否分支机构
     */
    private String originalSubOrg;

    /**
     * 匹配社会信用代码返回json
     */
    private String creditCodeMatchJson;

    /**
     * 匹配单位名称返回json
     */
    private String companyNameMatchJson;

    /**
     * 匹配标识
     * 1：单位名称和社会信用代码全部匹配成功
     * 2：社会信用代码匹配成功，单位名称未匹配成功
     * 3：单位名称匹配成功，社会信用代码未匹配成功
     * 4：单位名称和社会信用代码全部匹配失败
     * 5：单位名称和社会信用代码都能匹配到天眼查，但不是同一个单位
     */
    private Integer matchTag;

    /**
     * 处理时间
     */
    private String processTime;

    /**
     * AI分析结果
     */
    private String aiAnalysis;

    /**
     * 是否已处理（用于标记异常记录已处理）
     */
    private Boolean processed;

    /**
     * 选择的企业名称
     */
    private String selectedCompanyName;

    /**
     * 选择的社会信用代码
     */
    private String selectedCreditCode;

    /**
     * 选择的企业类型
     */
    private String selectedCompanyType;

    /**
     * 选择的注册地址
     */
    private String selectedBase;

    /**
     * 选择的法定代表人
     */
    private String selectedLegalPersonName;

    /**
     * 选择的企业状态
     */
    private String selectedRegStatus;

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getOriginalCompanyName() {
        return originalCompanyName;
    }

    public void setOriginalCompanyName(String originalCompanyName) {
        this.originalCompanyName = originalCompanyName;
    }

    public String getOriginalCreditCode() {
        return originalCreditCode;
    }

    public void setOriginalCreditCode(String originalCreditCode) {
        this.originalCreditCode = originalCreditCode;
    }

    public String getOriginalDelMark() {
        return originalDelMark;
    }

    public void setOriginalDelMark(String originalDelMark) {
        this.originalDelMark = originalDelMark;
    }

    public String getOriginalSubOrg() {
        return originalSubOrg;
    }

    public void setOriginalSubOrg(String originalSubOrg) {
        this.originalSubOrg = originalSubOrg;
    }

    public String getCreditCodeMatchJson() {
        return creditCodeMatchJson;
    }

    public void setCreditCodeMatchJson(String creditCodeMatchJson) {
        this.creditCodeMatchJson = creditCodeMatchJson;
    }

    public String getCompanyNameMatchJson() {
        return companyNameMatchJson;
    }

    public void setCompanyNameMatchJson(String companyNameMatchJson) {
        this.companyNameMatchJson = companyNameMatchJson;
    }

    public Integer getMatchTag() {
        return matchTag;
    }

    public void setMatchTag(Integer matchTag) {
        this.matchTag = matchTag;
    }

    public String getProcessTime() {
        return processTime;
    }

    public void setProcessTime(String processTime) {
        this.processTime = processTime;
    }

    public String getAiAnalysis() {
        return aiAnalysis;
    }

    public void setAiAnalysis(String aiAnalysis) {
        this.aiAnalysis = aiAnalysis;
    }

    public Boolean getProcessed() {
        return processed;
    }

    public void setProcessed(Boolean processed) {
        this.processed = processed;
    }

    public String getSelectedCompanyName() {
        return selectedCompanyName;
    }

    public void setSelectedCompanyName(String selectedCompanyName) {
        this.selectedCompanyName = selectedCompanyName;
    }

    public String getSelectedCreditCode() {
        return selectedCreditCode;
    }

    public void setSelectedCreditCode(String selectedCreditCode) {
        this.selectedCreditCode = selectedCreditCode;
    }

    public String getSelectedCompanyType() {
        return selectedCompanyType;
    }

    public void setSelectedCompanyType(String selectedCompanyType) {
        this.selectedCompanyType = selectedCompanyType;
    }

    public String getSelectedBase() {
        return selectedBase;
    }

    public void setSelectedBase(String selectedBase) {
        this.selectedBase = selectedBase;
    }

    public String getSelectedLegalPersonName() {
        return selectedLegalPersonName;
    }

    public void setSelectedLegalPersonName(String selectedLegalPersonName) {
        this.selectedLegalPersonName = selectedLegalPersonName;
    }

    public String getSelectedRegStatus() {
        return selectedRegStatus;
    }

    public void setSelectedRegStatus(String selectedRegStatus) {
        this.selectedRegStatus = selectedRegStatus;
    }
}
