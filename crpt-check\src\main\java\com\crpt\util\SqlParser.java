package com.crpt.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL解析工具类
 */
public class SqlParser {

    private static final Logger log = LoggerFactory.getLogger(SqlParser.class);

    /**
     * 解析SQL中的表名
     */
    public static String parseTableName(String sql) {
        if (!StringUtils.hasText(sql)) {
            return null;
        }

        try {
            // 改进的表名解析逻辑，支持复杂的FROM子句（包含JOIN）
            // 首先尝试匹配简单的FROM子句
            Pattern simplePattern = Pattern.compile("FROM\\s+([^\\s\\n\\r,]+)", Pattern.CASE_INSENSITIVE);
            Matcher simpleMatcher = simplePattern.matcher(sql);

            if (simpleMatcher.find()) {
                String tableName = simpleMatcher.group(1).trim();
                // 移除可能的别名（如果有AS关键字或空格后跟别名）
                tableName = tableName.split("\\s+")[0];
                // 移除可能的数据库前缀（如schema.table）
                if (tableName.contains(".")) {
                    tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
                }
                return tableName;
            }

            // 如果简单匹配失败，尝试匹配包含JOIN的复杂FROM子句
            // 匹配 FROM table_name alias LEFT JOIN ... 的模式
            Pattern complexPattern = Pattern.compile("FROM\\s+([^\\s\\n\\r]+)\\s+([A-Za-z]\\w*)\\s+(?:LEFT|RIGHT|INNER|FULL)?\\s*JOIN", Pattern.CASE_INSENSITIVE);
            Matcher complexMatcher = complexPattern.matcher(sql);

            if (complexMatcher.find()) {
                String tableName = complexMatcher.group(1).trim();
                // 移除可能的数据库前缀（如schema.table）
                if (tableName.contains(".")) {
                    tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
                }
                return tableName;
            }
        } catch (Exception e) {
            log.warn("解析SQL表名失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 解析SQL中的字段映射
     * 返回Map<别名, 原始列名>
     */
    public static Map<String, String> parseFieldMappings(String sql) {
        Map<String, String> fieldMappings = new HashMap<>();

        if (!StringUtils.hasText(sql)) {
            return fieldMappings;
        }

        try {
            // 提取SELECT子句
            Pattern selectPattern = Pattern.compile("SELECT\\s+(.*?)\\s+FROM", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher selectMatcher = selectPattern.matcher(sql);

            if (!selectMatcher.find()) {
                return fieldMappings;
            }

            String selectClause = selectMatcher.group(1);
            String[] fields = selectClause.split(",");

            for (String field : fields) {
                field = field.trim();

                // 处理 AS 别名
                Pattern asPattern = Pattern.compile("(.+?)\\s+AS\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
                Matcher asMatcher = asPattern.matcher(field);

                if (asMatcher.find()) {
                    String originalColumn = asMatcher.group(1).trim();
                    String alias = asMatcher.group(2).trim().toUpperCase();

                    // 如果原始列名有表别名前缀，去掉前缀，只保留实际列名
                    if (originalColumn.contains(".")) {
                        String[] parts = originalColumn.split("\\.");
                        if (parts.length == 2) {
                            originalColumn = parts[1].trim(); // 只保留列名部分
                        }
                    }

                    fieldMappings.put(alias, originalColumn);
                } else {
                    // 没有别名的情况，需要改进字段名解析
                    String columnName = field.trim();

                    // 处理表别名前缀，如 B.CREDIT_CODE, BB.UNIT_NAME, H.RID
                    if (columnName.contains(".")) {
                        String[] parts = columnName.split("\\.");
                        if (parts.length == 2) {
                            String actualColumnName = parts[1].trim();

                            // 根据常见的字段名模式进行映射
                            String mappingKey = mapColumnToStandardAlias(actualColumnName);

                            // 存储时保留实际列名，不包含表别名前缀
                            // 确保能够正确识别所有常见的表别名前缀（B., BB., H.等）
                            fieldMappings.put(mappingKey, actualColumnName);

                            log.debug("处理表别名前缀字段: {} -> 映射键: {}, 实际列名: {}",
                                    columnName, mappingKey, actualColumnName);
                        }
                    } else {
                        // 没有表前缀的字段
                        String mappingKey = mapColumnToStandardAlias(columnName);
                        fieldMappings.put(mappingKey, columnName);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析SQL字段映射失败: {}", e.getMessage());
        }

        return fieldMappings;
    }

    /**
     * 将列名映射到标准别名
     */
    private static String mapColumnToStandardAlias(String columnName) {
        String upperColumnName = columnName.toUpperCase();

        // 社会信用代码字段映射
        if (upperColumnName.contains("CREDIT") && upperColumnName.contains("CODE")) {
            return "CODE";
        }
        if (upperColumnName.contains("INSTITUTION") && upperColumnName.contains("CODE")) {
            return "CODE";
        }

        // 企业名称字段映射
        if (upperColumnName.contains("UNIT") && upperColumnName.contains("NAME")) {
            return "NAME";
        }
        if (upperColumnName.contains("COMPANY") && upperColumnName.contains("NAME")) {
            return "NAME";
        }
        if (upperColumnName.contains("CRPT") && upperColumnName.contains("NAME")) {
            return "NAME";
        }

        // 业务主键字段映射
        if (upperColumnName.equals("RID") || upperColumnName.equals("ID")) {
            return "ID";
        }

        // 删除标记字段映射
        if (upperColumnName.contains("IF") && upperColumnName.contains("DELETE")) {
            return "DEL_MARK";
        }
        if (upperColumnName.contains("DEL") && upperColumnName.contains("MARK")) {
            return "DEL_MARK";
        }

        // 分支机构字段映射 - 增强识别逻辑
        // 优先匹配完整的字段名
        if (upperColumnName.equals("IF_SUB_ORG")) {
            return "IF_SUB_ORG";
        }
        if (upperColumnName.equals("IF_BRANCH")) {
            return "IF_SUB_ORG";
        }
        // 模糊匹配分支机构相关字段
        if (upperColumnName.contains("IF") && upperColumnName.contains("BRANCH")) {
            return "IF_SUB_ORG";
        }
        if (upperColumnName.contains("SUB") && upperColumnName.contains("ORG")) {
            return "IF_SUB_ORG";
        }
        // 支持更多分支机构字段模式
        if (upperColumnName.contains("BRANCH") && (upperColumnName.contains("FLAG") || upperColumnName.contains("MARK"))) {
            return "IF_SUB_ORG";
        }
        if (upperColumnName.contains("SUB") && (upperColumnName.contains("COMPANY") || upperColumnName.contains("UNIT"))) {
            return "IF_SUB_ORG";
        }

        // 默认使用原字段名作为键
        return upperColumnName;
    }

    /**
     * 解析用于数据提取的列名（别名）
     * 从查询结果中提取数据时使用别名
     */
    public static String resolveAliasForDataExtraction(Map<String, String> fieldMappings, String userRealColumnName, String defaultAlias) {
        // 1. 如果用户指定了真实列名，查找对应的别名
        if (StringUtils.hasText(userRealColumnName)) {
            // 在字段映射中查找真实列名对应的别名
            for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
                String alias = entry.getKey();
                String realColumn = entry.getValue();
                if (userRealColumnName.equalsIgnoreCase(realColumn)) {
                    log.debug("找到真实列名 {} 对应的别名: {}", userRealColumnName, alias);
                    return alias;
                }
            }
            log.debug("未找到真实列名 {} 对应的别名，尝试使用默认策略", userRealColumnName);
        }

        // 2. 如果用户没有输入或找不到对应别名，尝试使用默认别名
        if (fieldMappings.containsKey(defaultAlias.toUpperCase())) {
            log.debug("使用默认别名: {}", defaultAlias);
            return defaultAlias;
        }

        // 3. 查找可能的匹配（模糊匹配）
        for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
            String alias = entry.getKey();
            if (alias.contains(defaultAlias.toUpperCase())) {
                log.debug("通过模糊匹配找到别名: {} -> {}", defaultAlias, alias);
                return alias;
            }
        }

        // 4. 如果都没找到，返回默认别名
        log.debug("未找到匹配的别名，使用默认别名: {}", defaultAlias);
        return defaultAlias;
    }

    /**
     * 解析用于更新SQL的真实列名
     * 生成更新SQL时使用真实列名
     */
    public static String resolveRealColumnForUpdate(Map<String, String> fieldMappings, String userRealColumnName, String defaultAlias) {
        // 1. 如果用户指定了真实列名，直接使用
        if (StringUtils.hasText(userRealColumnName)) {
            log.debug("使用用户指定的真实列名: {}", userRealColumnName);
            return userRealColumnName;
        }

        // 2. 如果用户没有输入，从字段映射中查找默认别名对应的真实列名
        if (fieldMappings.containsKey(defaultAlias.toUpperCase())) {
            String realColumn = fieldMappings.get(defaultAlias.toUpperCase());
            log.debug("从SQL解析中找到默认别名 {} 对应的真实列名: {}", defaultAlias, realColumn);
            return realColumn;
        }

        // 3. 查找可能的匹配（模糊匹配）
        for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
            String alias = entry.getKey();
            if (alias.contains(defaultAlias.toUpperCase())) {
                String realColumn = entry.getValue();
                log.debug("通过模糊匹配找到别名 {} 对应的真实列名: {}", alias, realColumn);
                return realColumn;
            }
        }

        // 4. 如果都没找到，返回默认别名（作为真实列名）
        log.debug("未找到匹配的真实列名，使用默认别名作为真实列名: {}", defaultAlias);
        return defaultAlias;
    }

    /**
     * 生成更新SQL语句 - 支持新的语义化占位符
     */
    public static String generateUpdateSql(String updateSqlTemplate, String tableName,
                                         String businessKey, String businessKeyColumn,
                                         String newCreditCode, String newCompanyName,
                                         String oldCreditCode, String oldCompanyName) {
        if (!StringUtils.hasText(updateSqlTemplate)) {
            return null;
        }

        try {
            String updateSql = updateSqlTemplate;

            // 替换新的语义化占位符
            updateSql = updateSql.replace("{tableName}", tableName != null ? tableName : "");
            updateSql = updateSql.replace("{businessKey}", businessKey != null ? businessKey : "");
            updateSql = updateSql.replace("{businessKeyColumn}", businessKeyColumn != null ? businessKeyColumn : "");
            updateSql = updateSql.replace("{newCreditCode}", newCreditCode != null ? newCreditCode : "");
            updateSql = updateSql.replace("{newCompanyName}", newCompanyName != null ? newCompanyName : "");
            updateSql = updateSql.replace("{oldCreditCode}", oldCreditCode != null ? oldCreditCode : "");
            updateSql = updateSql.replace("{oldCompanyName}", oldCompanyName != null ? oldCompanyName : "");

            // 保持向后兼容性 - 支持旧的占位符格式（如果新占位符不存在）
            if (updateSql.contains("{creditCode}") && newCreditCode != null) {
                updateSql = updateSql.replace("{creditCode}", newCreditCode);
            }
            if (updateSql.contains("{companyName}") && newCompanyName != null) {
                updateSql = updateSql.replace("{companyName}", newCompanyName);
            }

            return updateSql;
        } catch (Exception e) {
            log.warn("生成更新SQL失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 根据查询SQL自动生成更新SQL模板
     * 确保使用与查询SQL一致的列名
     */
    public static String generateUpdateSqlTemplate(String querySql, String tableName,
                                                 String creditCodeColumn, String companyNameColumn,
                                                 String businessKeyColumn) {
        if (!StringUtils.hasText(querySql)) {
            return null;
        }

        try {
            // 解析字段映射
            Map<String, String> fieldMappings = parseFieldMappings(querySql);

            // 确定实际的列名（优先使用用户指定的，否则从SQL解析）
            String actualCreditCodeColumn = StringUtils.hasText(creditCodeColumn) ?
                creditCodeColumn : fieldMappings.get("CODE");
            String actualCompanyNameColumn = StringUtils.hasText(companyNameColumn) ?
                companyNameColumn : fieldMappings.get("NAME");
            String actualBusinessKeyColumn = StringUtils.hasText(businessKeyColumn) ?
                businessKeyColumn : fieldMappings.get("ID");

            // 如果没有解析到表名，使用传入的表名
            String actualTableName = StringUtils.hasText(tableName) ? tableName : parseTableName(querySql);

            // 生成更新SQL模板
            StringBuilder updateSqlBuilder = new StringBuilder();
            updateSqlBuilder.append("UPDATE ").append(actualTableName != null ? actualTableName : "{tableName}").append(" SET ");

            boolean hasSetClause = false;
            if (StringUtils.hasText(actualCreditCodeColumn)) {
                updateSqlBuilder.append(actualCreditCodeColumn).append(" = '{newCreditCode}'");
                hasSetClause = true;
            }

            if (StringUtils.hasText(actualCompanyNameColumn)) {
                if (hasSetClause) {
                    updateSqlBuilder.append(",\n    ");
                }
                updateSqlBuilder.append(actualCompanyNameColumn).append(" = '{newCompanyName}'");
                hasSetClause = true;
            }

            if (hasSetClause) {
                updateSqlBuilder.append("\nWHERE ");
                if (StringUtils.hasText(actualBusinessKeyColumn)) {
                    updateSqlBuilder.append(actualBusinessKeyColumn).append(" = '{businessKey}'");
                } else {
                    updateSqlBuilder.append("{businessKeyColumn} = '{businessKey}'");
                }
            } else {
                // 如果没有可更新的字段，返回一个基本模板
                return "UPDATE {tableName} SET \n    {creditCodeColumn} = '{newCreditCode}',\n    {companyNameColumn} = '{newCompanyName}'\nWHERE {businessKeyColumn} = '{businessKey}'";
            }

            return updateSqlBuilder.toString();
        } catch (Exception e) {
            log.warn("自动生成更新SQL模板失败: {}", e.getMessage());
            return "UPDATE {tableName} SET \n    {creditCodeColumn} = '{newCreditCode}',\n    {companyNameColumn} = '{newCompanyName}'\nWHERE {businessKeyColumn} = '{businessKey}'";
        }
    }

    /**
     * 解析字段信息并存储到日志中
     */
    public static Map<String, Object> parseFieldInfo(String sql, String tableName,
                                                   String creditCodeColumn, String companyNameColumn,
                                                   String businessKeyColumn, String delMarkColumn,
                                                   String subOrgColumn) {
        Map<String, Object> fieldInfo = new HashMap<>();
        
        // 解析SQL中的字段映射
        Map<String, String> fieldMappings = parseFieldMappings(sql);
        
        // 解析表名
        String parsedTableName = parseTableName(sql);
        if (StringUtils.hasText(parsedTableName)) {
            fieldInfo.put("parsedTableName", parsedTableName);
        }
        
        // 解析用于数据提取的别名（从查询结果中提取数据）
        String aliasForCreditCode = resolveAliasForDataExtraction(fieldMappings, creditCodeColumn, "CODE");
        String aliasForCompanyName = resolveAliasForDataExtraction(fieldMappings, companyNameColumn, "NAME");
        String aliasForBusinessKey = resolveAliasForDataExtraction(fieldMappings, businessKeyColumn, "ID");
        String aliasForDelMark = resolveAliasForDataExtraction(fieldMappings, delMarkColumn, "DEL_MARK");
        String aliasForSubOrg = resolveAliasForDataExtraction(fieldMappings, subOrgColumn, "SUB_ORG");

        // 解析用于更新SQL的真实列名
        String realCreditCodeColumn = resolveRealColumnForUpdate(fieldMappings, creditCodeColumn, "CODE");
        String realCompanyNameColumn = resolveRealColumnForUpdate(fieldMappings, companyNameColumn, "NAME");
        String realBusinessKeyColumn = resolveRealColumnForUpdate(fieldMappings, businessKeyColumn, "ID");
        String realDelMarkColumn = resolveRealColumnForUpdate(fieldMappings, delMarkColumn, "DEL_MARK");
        String realSubOrgColumn = resolveRealColumnForUpdate(fieldMappings, subOrgColumn, "SUB_ORG");
        
        // 存储用于数据提取的别名
        if (StringUtils.hasText(aliasForCreditCode)) {
            fieldInfo.put("creditCodeColumn", aliasForCreditCode);
        }
        if (StringUtils.hasText(aliasForCompanyName)) {
            fieldInfo.put("companyNameColumn", aliasForCompanyName);
        }
        if (StringUtils.hasText(aliasForBusinessKey)) {
            fieldInfo.put("businessKeyColumn", aliasForBusinessKey);
        }
        if (StringUtils.hasText(aliasForDelMark)) {
            fieldInfo.put("delMarkColumn", aliasForDelMark);
        }
        if (StringUtils.hasText(aliasForSubOrg)) {
            fieldInfo.put("subOrgColumn", aliasForSubOrg);
        }

        // 存储用于更新SQL的真实列名
        if (StringUtils.hasText(realCreditCodeColumn)) {
            fieldInfo.put("realCreditCodeColumn", realCreditCodeColumn);
        }
        if (StringUtils.hasText(realCompanyNameColumn)) {
            fieldInfo.put("realCompanyNameColumn", realCompanyNameColumn);
        }
        if (StringUtils.hasText(realBusinessKeyColumn)) {
            fieldInfo.put("realBusinessKeyColumn", realBusinessKeyColumn);
        }
        if (StringUtils.hasText(realDelMarkColumn)) {
            fieldInfo.put("realDelMarkColumn", realDelMarkColumn);
        }
        if (StringUtils.hasText(realSubOrgColumn)) {
            fieldInfo.put("realSubOrgColumn", realSubOrgColumn);
        }
        
        // 存储原始字段映射
        fieldInfo.put("fieldMappings", fieldMappings);
        
        return fieldInfo;
    }
}
