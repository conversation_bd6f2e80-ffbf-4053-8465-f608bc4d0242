package com.crpt.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文档控制器 - 处理操作手册等文档的访问
 */
@Controller
@RequestMapping("/docs")
public class DocumentController {

    private static final Logger log = LoggerFactory.getLogger(DocumentController.class);

    /**
     * 获取用户操作手册
     */
    @GetMapping("/用户操作手册.md")
    public ResponseEntity<String> getUserManual() {
        try {
            // 首先尝试从项目根目录的docs文件夹读取
            Path manualPath = Paths.get("docs/用户操作手册.md");
            
            if (Files.exists(manualPath)) {
                // 从文件系统读取
                byte[] bytes = Files.readAllBytes(manualPath);
                String content = new String(bytes, StandardCharsets.UTF_8);
                log.info("从文件系统读取操作手册: {}", manualPath.toAbsolutePath());
                
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "text/markdown; charset=utf-8")
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"用户操作手册.md\"")
                        .body(content);
            } else {
                // 尝试从classpath读取
                try {
                    Resource resource = new ClassPathResource("docs/用户操作手册.md");
                    if (resource.exists()) {
                        // JDK 1.8 compatible way to read InputStream
                        InputStream inputStream = resource.getInputStream();
                        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                        int nRead;
                        byte[] data = new byte[1024];
                        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                            buffer.write(data, 0, nRead);
                        }
                        buffer.flush();
                        String content = new String(buffer.toByteArray(), StandardCharsets.UTF_8);
                        log.info("从classpath读取操作手册");
                        
                        return ResponseEntity.ok()
                                .header(HttpHeaders.CONTENT_TYPE, "text/markdown; charset=utf-8")
                                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"用户操作手册.md\"")
                                .body(content);
                    }
                } catch (IOException e) {
                    log.warn("从classpath读取操作手册失败: {}", e.getMessage());
                }
                
                // 如果都找不到，返回默认内容
                String defaultContent = generateDefaultManual();
                log.warn("操作手册文件不存在，返回默认内容");
                
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "text/markdown; charset=utf-8")
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"用户操作手册.md\"")
                        .body(defaultContent);
            }
            
        } catch (Exception e) {
            log.error("读取操作手册失败", e);
            
            // 返回错误信息
            String errorContent = "# 操作手册加载失败\n\n" +
                    "抱歉，无法加载操作手册。请联系系统管理员。\n\n" +
                    "错误信息: " + e.getMessage();
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, "text/markdown; charset=utf-8")
                    .body(errorContent);
        }
    }

    /**
     * 生成默认操作手册内容
     */
    private String generateDefaultManual() {
        return "# 天眼查企业信息匹配系统 - 用户操作手册\n\n" +
                "## 系统概述\n\n" +
                "天眼查企业信息匹配系统是一个基于Spring Boot开发的企业信息核验工具。\n\n" +
                "## 主要功能\n\n" +
                "1. **企业信息匹配检查** - 对数据库中的企业信息进行匹配验证\n" +
                "2. **日志分析** - 分析匹配结果，提供统计信息\n" +
                "3. **企业信息选择** - 对匹配失败的记录进行人工选择\n" +
                "4. **AI智能分析** - 提供匹配失败原因分析（可选）\n\n" +
                "## 快速开始\n\n" +
                "### 1. 执行匹配检查\n" +
                "- 点击\"开始检查\"按钮\n" +
                "- 选择数据库类型（MySQL/Oracle）\n" +
                "- 输入查询SQL语句\n" +
                "- 配置字段映射\n" +
                "- 启动检查任务\n\n" +
                "### 2. 分析结果\n" +
                "- 点击\"分析日志\"按钮\n" +
                "- 选择要分析的日志文件\n" +
                "- 查看统计信息和详细记录\n" +
                "- 对异常记录进行处理\n\n" +
                "## 注意事项\n\n" +
                "- SQL查询必须包含社会信用代码、单位名称、业务主键字段\n" +
                "- 建议使用字段别名：CODE（社会信用代码）、NAME（单位名称）、ID（业务主键）\n" +
                "- 系统会自动生成日志文件，可通过日志分析功能查看结果\n\n" +
                "---\n\n" +
                "**注意**: 这是默认的操作手册内容。完整的操作手册请联系系统管理员获取。";
    }
}
