package com.crpt.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AI分析配置
 */
@Component
@ConfigurationProperties(prefix = "ai.analysis")
public class AiAnalysisConfig {

    /**
     * 是否启用AI分析
     */
    private boolean enabled = false;

    /**
     * AI API地址（默认使用DeepSeek API）
     */
    private String apiUrl = "https://api.deepseek.com/chat/completions";

    /**
     * AI API密钥
     */
    private String apiKey;

    /**
     * AI模型名称（默认使用DeepSeek Chat模型）
     */
    private String model = "deepseek-chat";

    /**
     * 请求超时时间（秒）
     */
    private int timeout = 30;

    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
}
